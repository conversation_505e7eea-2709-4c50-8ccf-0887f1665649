<template>
  <div class="w-full">
    <div class="!w-full flex composite-input">
      <!-- 经度输入框 -->
      <bf-input ref="longitudeInputRef" v-model="wrapLon" :controls="false" class="grow" />

      <!-- 分隔符 -->
      <span class="separator">/</span>

      <!-- 纬度输入框 -->
      <bf-input v-model="wrapLat" :controls="false" class="grow" />
      <el-tooltip popper-class="bf-tooltip" placement="bottom" :content="$t('dialog.getLngLat')">
        <div class="iconfont bfdx-weizhi text-[20px] cursor-pointer flex-none mx-[10px]" @click="get_lonLat_func"></div>
      </el-tooltip>
    </div>
    <!-- 获取经纬度和范围的地图 -->
    <el-dialog v-model="mapVisible" fullscreen append-to-body :modal="false" :close-on-click-modal="false" :close-on-press-escape="false" :show-close="false">
      <base-map ref="baseMap" v-model:visible="mapVisible" class="lonLat-map-container" @init="onInitMap" @close="onCloseMap">
        <template #topCenter>
          <div class="get-coordinate-tips">
            {{ $t('map.clickMapGetCoordinates') }}
          </div>
        </template>
      </base-map>
    </el-dialog>
  </div>
</template>

<script>
  import baseMap from '@/components/common/BaseMap.vue'
  import { deferred } from '@/utils/bfutil'
  import { SelectLngLatControl } from '@/utils/map'
  import eventBus from '@/utils/eventBus'
  import bfInput from '@/components/bfInput/main'

  let lngLatMap
  const lngLatMapReady = deferred()
  export default {
    name: 'LonLat',
    emits: ['update:modelValue', 'update:lon', 'update:lat'],
    components: { baseMap, bfInput },
    props: {
      modelValue: {
        type: Object,
      },
      lon: {
        type: [Number, String],
      },
      lat: {
        type: [Number, String],
      },
    },
    data() {
      return {
        mapVisible: false,
        selectLngLatControl: new SelectLngLatControl(),
        isFocused: false,
      }
    },
    methods: {
      get_lonLat_func() {
        this.mapVisible = true
        this.selectLngLatControl.enable(false)

        const finish = () => {
          this.mapVisible = false
          lngLatMap.getCanvas().style.cursor = ''
        }
        const setLngLat = lngLat => {
          this.wrapLon = lngLat.lng
          this.wrapLat = lngLat.lat
        }
        const mapOnClick = evt => {
          setLngLat(evt.lngLat)
          onMapClose()
        }

        const onMapClose = () => {
          lngLatMap.off('click', mapOnClick)
          finish()
        }

        lngLatMapReady.then(() => {
          eventBus.once('map-close', onMapClose)
          lngLatMap.getCanvas().style.cursor = 'crosshair'
          lngLatMap.on('click', mapOnClick)
        })
      },
      onInitMap(map) {
        lngLatMap = map
        lngLatMapReady.resolve(true)
        this.$nextTick(() => {
          map.resize()
        })
      },
      onCloseMap() {
        this.mapVisible = false
        eventBus.emit('map-close')
      },
    },
    computed: {
      wrapLon: {
        get() {
          return this.lon ?? this.lonLat.lon
        },
        set(value) {
          this.$emit('update:lon', +value)
          if (this.lonLat) {
            this.lonLat.lon = +value
          }
        },
      },
      wrapLat: {
        get() {
          return this.lat ?? this.lonLat.lat
        },
        set(value) {
          this.$emit('update:lat', +value)
          if (this.lonLat) {
            this.lonLat.lat = +value
          }
        },
      },
      lonLat: {
        get() {
          return this.modelValue
        },
        set(value) {
          this.$emit('update:modelValue', value)
        },
      },
    },
  }
</script>

<style lang="scss">
  .composite-input {
    align-items: center;
    border: none;
    border-radius: 0;
    box-shadow: 0 0 0 2px rgba(148, 204, 232, 1) inset;
    cursor: text;

    &:hover {
      box-shadow: 0 0 0 2px rgba(148, 204, 232, 0.7) inset;
    }

    .bf-input {
      .el-input__wrapper {
        box-shadow: unset !important;
        input {
          text-align: center;
        }
      }
    }

    & > :first-child.bf-input .el-input__wrapper {
      padding: 0 0 0 10px;
    }
    & > :nth-child(3).bf-input .el-input__wrapper {
      padding: 0 10px 0 0;
    }
  }

  /* 聚焦状态：边框变色，和 el-input 行为一致 */
  .composite-input.is-focused {
    border-color: var(--el-color-primary);
  }

  /* 分隔符样式 */
  .separator {
    padding: 0 5px;
    color: var(--el-text-color-placeholder);
    flex-shrink: 0; /* 防止在 flex 布局中被压缩 */
  }
  .lonLat-map-container {
    position: fixed;
    inset: 0;
    z-index: 1000;
    background-color: #fff;
  }
</style>
