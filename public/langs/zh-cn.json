{"auth": {"applyAuth": "申请授权", "authDescription": "授权说明", "authExpiredAlert": "授权信息已过期，请重新申请授权！", "authFileError": "授权文件错误", "authorizedContent": "授权内容", "authorizedModules": "模块授权", "authorizedNumber": "数量授权", "authorizedTime": "授权时间", "expireTime": "到期时间", "importAuthFile": "导入授权文件", "logout": "退出登录", "modules": {"controller": "设备", "device": "终端", "max-controllers": "设备授权数量", "max-devices": "终端授权数量", "max-users": "用户授权数量", "mod-dispatch": "联网调度", "mod-phone-gateway": "电话网关", "mod-record": "录音功能", "mod-rfid": "巡查功能", "mod-svt": "虚拟集群", "mod-traditional-dmr": "常规DMR手台接入", "user": "用户"}, "noAuthAlert": "没有授权信息，请先申请授权！", "noReminderToday": "今日不再提醒", "noSpecifiedModuleAuth": "没有({module})授权信息，请先申请授权！", "permanent": "永久授权", "projName": "项目名称", "queryAuthFailed": "查询授权信息失败", "reapplyAuthTips": "距离授权到期时间剩余{days}天，是否重新申请授权？", "remainingExpirationTime": "({days}天)", "requestAuthSuccess": "申请授权成功", "unlimited": "无限制"}, "dataTable": {"BSGroupCall_2": "2级领导", "BSGroupCall_23": "2级、3级领导", "BSGroupCall_3": "3级领导", "IPAddress": "IP地址", "PTY": "合格率", "acPowerOffAlarm": "交流AC断电报警", "alarmConditions": "接警情况", "alarmDevice": "报警设备", "alarmTime": "报警时间", "alarmType": "报警类型", "alarmUserName": "报警用户", "allChannel": "所有信道", "allDevices": "所有设备", "allSys": "系统全呼", "antiDismantlingAlarm": "防拆报警", "backToWorkPrompt": "回岗提示", "batteryOn": "电池开机", "callChannel": "通话信道", "callTarget": "呼叫目标", "callTime": "通话时间", "checkCount": "应查数目", "checkDevice": "检查设备", "checkResult": "检查结果", "checkTime": "检查时间", "checkTimeEarly": "最早检查时间", "checkTimeEnd": "最晚检查时间", "checkUser": "检查人员", "confirmTime": "确认时间", "ctrlAlarm": "故障", "ctrlDataTime": "数据时间", "departureAlarm": "离岗报警", "deviceParentName": "终端所属单位", "direction": "方向", "dispatchTarget": "被调度者", "dispatchTime": "调度时间", "dispatchTypeName": "调度类型", "duration": "时长(秒)", "emergency": "紧急报警", "endRoomOrgName": "结束会议单位", "endRoomPerson": "结束会议室者", "endWork": "下班", "eventType": "事件类型", "exportExcel": "导出 Excel", "fail": "不合格", "fanExeception": "风扇异常", "gpsAlarm": "定位故障报警", "gpsNotInstalled": "GPS未安装", "gpsNotSynchronous": "GPS未同步", "gpsTime": "定位时间", "groupCall_12L": "所有1、2级领导", "groupCall_1L": "所有1级领导", "groupCall_2L": "所有2级领导", "inboundAlarm": "入界报警", "infraredSensorAlarm": "红外线感应报警", "inputcontent": "请输入内容", "inspector": "巡逻人员", "lowVol": "电压过低", "lowVoltageAlarm": "欠压报警", "mobileMonitoringStayAlarm": "移动监控停留报警", "noLocationAlarm": "紧急报警(无定位)", "offNetworkAlarm": "脱网报警", "offline": "下线", "online": "上线", "onlineType": "在线类型", "operation": "用户操作", "outboundsAlarm": "出界报警", "overHeat": "温度过高", "overVol": "电压过高", "pass": "合格", "patrolTime": "巡逻时间", "pllReceiveException": "PLL接收异常", "pllTxException": "PLL发送异常", "pointNo": "巡逻点编号", "powerOff": "关机", "powerOn": "开机", "processContext": "处理结果", "processTime": "处理时间", "processor": "处理人员", "punchInWork": "上班中打卡", "readTime": "读卡时间", "readUser": "读卡用户", "receiveName": "收信人", "receiveTime": "接收时间", "receiver": "接收数据中继", "registered": "已注册", "removeSimulcastSystem": "移出同播系统", "repeater": "收信中继", "resetOn": "欠压复位开机", "selAlarmNote": "查询报警历史记录", "selDetailNote": "查询巡逻历史记录", "selGpsNote": "查询定位轨迹记录", "selOndutyNote": "查询交班历史记录", "selSoundNote": "查询通话录音历史记录", "selSwitchNote": "查询开、关机历史记录", "senderName": "发送人", "signalInterference": "信号干扰", "smsNo": "短信序号", "soundFileName": "录音文件名", "soundSensorAlarm": "声音感应报警", "sourceController": "主呼设备", "speed": "速度", "sponsor": "发起者", "standingWaveException": "天线（驻波）异常", "startRoomOrgName": "发起会议单位", "startRoomPerson": "发起会议室者", "startWork": "上班", "statusException": "状态异常", "statusReport": "状态报告", "syncCall": "动态组呼", "sysCenter": "中心", "targetChannel": "目标信道", "times": "时间", "turnOffAlertMode": "关闭警戒模式", "turnOnAlertMode": "开启警戒模式", "type": "类型", "userName": "用户名", "userOrg": "用户所属单位", "walkingMobileMonitorPrompt": "移动监控走动提示", "weeks": "星期", "actions": "操作"}, "dialog": {"1month": "1月", "1week": "1周", "1year": "1年", "3day": "3天", "BaseStationPoint": "基站巡逻点", "DMRDevice": "车载台", "Fri": "五", "Friday": "星期五", "Gsource": "虚拟点", "Hsource": "有源点", "IKnow": "好,我知道了", "Mon": "一", "Monday": "星期一", "Nsource": "无源点", "PowerOnPwdErrorThreshold": "开机密码错误阈值", "Quieting": "静噪", "Sat": "六", "Saturday": "星期六", "Sun": "日", "Sunday": "星期日", "Thur": "四", "Thursday": "星期四", "Tues": "二", "Tuesday": "星期二", "Wed": "三", "Wednesday": "星期三", "action": "操作", "activeRFID": "有源RFID", "add": "添加", "addMapPoint": "增加地图标记点", "addPreMadeSms": "添加短信", "addStrangeNumberAuto": "自动添加陌生人到通讯录", "addressBook": "通讯录", "addressBookList": "通讯录列表", "addressBookSettings": "通讯录设置", "advancedDynamicencryption": "高级动态加密", "agree": "同意", "aheadTime": "最快到达时间", "alarmSet": "报警设置", "alarmSt": "报警状态", "alertTitle": "提示", "allContacts": "所有联系人", "allowCallOffline": "允许呼叫不在线目标", "allowCallOfflineInfo": "该功能开启后，可以对不在线目标进行呼叫。", "allowGeneralDMROnlineCall": "允许常规DMR终端接入", "allowLoginManage": "允许登录调度管理", "allowOffNetwork": "允许脱网", "allowOnlineCalls": "允许联网呼叫", "alreadyApplied": "已申请", "alreadyAuth": "已授权", "always": "总是生效", "analog": "模拟", "analogAndDigital": "模拟和数字", "analogChannel": "模拟信道", "analogGatewayTerminal": "模拟网关终端", "analogHangTime": "模拟呼叫挂起时间({unit})", "annex": "附件", "answer": "应答", "answeredCall": "已应答呼叫", "area": "区域", "areaData": "区域数据", "areaDown": "区域下调", "areaName": "区域名称", "areaNumber": "区域序号", "areaSearch": "区域查找", "areaUp": "区域上调", "authDevices": "授权终端", "authorizationName": "授权名称", "autoCardReading": "到位自动读卡", "autoListeningTime": "自动监听时间(s)", "autoPlaySms": "自动播放短信", "autoPositioningTime": "自动定位时间(s)", "autoSignalDurtion": "自动发送信标时长({unit})", "autoSignalInterval": "自动发送信标间隔({unit})", "automode": "自动模式", "availableColorCode": "可用彩色码", "back": "返回", "backlight": "背光", "bandpassFilter": "带通滤波器", "baseGroupCallScheduling": "已基站群呼调度", "baseMobileRadio": "基地台", "baseStation": "基站", "basicConfig": "基础配置", "batchSetting": "批量复制", "batchSettingChannel": "批量复制信道", "batteryVoltage": "电池电压", "bdGateId": "北斗网关ID", "bdsSwitch": "BDS开关", "beidouAddressBook": "北斗通讯录", "beidouContact": "北斗联系人", "beidouNav": "北斗导航", "beidouNumber": "北斗号码", "beidouPositionInfo": "北斗定位信息", "beidouService": "北斗服务", "beidouSetting": "北斗设置", "beidouSms": "北斗短信", "beidouSwitch": "北斗开关", "blackList": "黑名单", "blackWhiteList": "黑白名单", "bootInterface": "开机界面", "buttonDefinition": "按键定义", "callBackTarget": "回呼目标", "callPermissionIndication": "呼叫允许指示", "callRecord": "呼叫记录", "callReminder": "呼叫提示", "callTarget": "通话目标", "callTransfer": "电话转接", "callType": "呼叫类型", "calledDevice": "主呼终端", "calledTarget": "被呼目标", "callingContact": "呼叫联系人", "canTalk": "可以插话", "cancel": "取消", "cancelCenterCHCtrl": "取消切换信道", "cancelEGChannel": "取消紧急信道调度", "cancelListen": "关闭监听", "cancelPending": "取消挂起", "cancelSetting": "取消设置", "centralCtrlLocate": "已监控定位", "chConfigSwitch": "信道配置开关", "chDisplay": "信道显示", "chDisplayMode": "信道显示模式", "chDownSwitch": "信道向下切换", "chHangTime": "信道挂起时间({unit})", "chId": "信道ID", "chName": "信道名称", "chType": "信道类型", "chUpSwitch": "信道向上切换", "channel": "信道", "channelBandwidth": "信道带宽", "channelConfigPassword": "信道配置密码", "channelCtrl": "调度信道", "channelDown": "信道下调", "channelGroupCallSD": "已信道群呼调度", "channelIdle": "信道空闲", "channelIdleIndication": "信道空闲指示", "channelList": "信道列表", "channelManager": "信道管理", "channelSetting": "信道设置", "channelShiftLevel": "频道值班级别", "channelUp": "信道上调", "checkCount": "巡逻次数", "checkDate": "检查日期", "checkLineName": "巡逻线路名称", "checkStartTime": "开始检查时间", "childRowIsEmpty": "当前行暂无详细信息", "chipAutoResMechanism": "采用芯片自动应答机制", "chooseDate": "选择日期", "cleanLogs": "清除日志", "clearAlarm": "解除报警", "clearChannelConfig": "清除信道配置", "clearCheckedImg": "清除选中的图片", "clearOnceIn10Minutes": "10分钟清理一次", "clickCheckImg": "点击选择图片", "close": "关闭", "closeMenuButton": "关闭菜单键", "cmdOpts": "命令选项", "colorCodes": "彩色码", "colorMapPoint": "颜色点", "commandAgent": "指挥坐席", "commander": "指挥机", "common": "常规", "configInfo": "配置信息", "configure": "配置", "confirm": "确定", "confirmDelBtn": "确定删除", "connect": "已连接", "contact": "联系人", "contactGrouping": "联系人分组", "contactList": "联系人列表", "controlTelephoneGateway": "TG810网关", "controllerPointJumpTip": "地图中心已跳转至当前设备标记点,请点击首页查看", "copy": "复制", "copyChannel": "复制信道", "cpVersion": "CP版本", "crossTOT": "越界限时(分)", "ctrlDMRID": "设备DMRID", "ctrlHistory": "设备事件历史", "curChannelInfo": "当前信道信息", "currentBattery": "当前电量", "currentChannel": "当前信道", "currentDevice": "当前终端设备", "customImg": "自定义图片", "customPiggybackPowerLeve": "自定义背负功率等级", "customPower": "自定义功率", "customVehiclePlv": "自定义车载功率等级", "customize": "自定义", "data": "数据", "dataChannel": "数据通道 {num}", "dataTrsCmdRes": "数传指令应答", "defCommunicationAddress": "默认通信地址", "defWorkingTimeSlot": "默认工作时隙", "default": "默认", "defaultChannel": "默认信道", "defaultFromArea": "默认从区域", "defaultMainArea": "默认主区域", "defaultUserArea": "默认用户区域", "delAllPrivilegeDevice": "取消终端全部位置权限", "delPrivilegeDevice": "取消终端位置权限", "delPrivilegeDeviceFail": "取消终端位置权限失败", "delPrivilegeDeviceSuccess": "取消终端位置权限成功", "delayTime": "最迟到达时间", "delete": "删除", "deleteContact": "删除联系人", "deletePrompt": "此操作将永久删除该数据，并可能包含其他重要信息，是否继续？", "detailOfLineMaster": "线路详情表", "details": "详情", "devAllStatus": "所有状态", "devDisAuthCode": "设备禁用鉴权码", "devEnAuthCode": "设备启用鉴权码", "devRemoteCtrl": "设备远程启用/禁用", "deviceActive": "设备激活", "deviceDMRID": "终端DMRID", "deviceDataTitle": "终端管理", "deviceDetect": "设备检测", "deviceDmrIdIsExisted": "该DMRID已存在", "deviceInfo": "设备信息", "deviceName": "设备名称", "devicePointJumpTip": "地图中心已跳转至当前终端标记点,请点击首页查看", "deviceRemoteDeath": "设备遥毙", "deviceSettings": "设备设置", "deviceTime": "设备时间", "deviceToWriteFrequency": "要写频的设备", "deviceType": "终端类型", "dialInList": "拨入名单", "dialOutList": "拨出名单", "digital": "数字", "digitalAnalogChannel": "数模信道", "digitalChannel": "数字信道", "digitalGatewayTerminal": "数字网关终端", "disListen": "禁听锁机", "disSL": "禁听禁发锁机", "disSend": "禁发锁机", "disabledAllLed": "禁用所有LED", "disconnect": "已断开", "dispatch": "调度历史", "displayFrequency": "显示频率", "displaysCallIdAndAlias": "显示呼叫ID和别名", "dmrAddressBook": "DMR通讯录", "dmrContact": "DMR联系人", "dmrIdNumber": "DMRID号码", "dmrIdResult": "十六进制的设备DMRID", "dmrService": "DMR服务", "dmrSms": "DMR短信", "dnsServerIp": "DNS服务器IP", "download": "下载", "draftBox": "草稿箱", "duplexSwitch": "双工开关", "dutyMachine": "值班机", "dynamicGroupCallSD": "已动态组呼调度", "dynamicOwnGroup": "动态归属组", "dynamicencryption": "动态加密", "early": "早到", "edit": "编辑", "editContacts": "编辑通讯录", "editContacts2": "编辑联系人", "editData": "可编辑数据", "editScanList": "编辑扫描列表", "editUserPerm": "编辑用户权限", "editZone": "编辑区域", "effectEndTime": "失效时间", "effectStartTime": "生效时间", "effectType": "生效类型", "effectiveDate": "有效日期", "electricFence": "电子围栏", "electricalAdjustmentFilter": "电调滤波器", "emerHangTime": "紧急呼叫挂起时间({unit})", "emergency": "紧急报警", "emergencyAlarm": "发生紧急报警", "emergencyDispatch": "已紧急全呼调度", "emission": "发射", "emissionNumber": "@:dialog.emission {num}(MHz)", "empty": "清空", "enable": "启用", "enableAutoMonitoring": "开启自动监听", "enableEGChannel": "开启紧急信道调度", "enableEmergencyAlarm": "开启紧急报警", "enableInAndOut": "启用跨界监控", "enableInCacnelOut": "启用入界监控", "enableListen": "启动监听", "enableOutCacnelIn": "启用出界监控", "enablePopUpBlacklist": "启用拨出黑名单", "enablePopUpWhitelist": "启用拨出白名单", "enableStackingBlacklist": "启用拨入黑名单", "enableStackingWhitelist": "启用拨入白名单", "endTime": "结束时间", "errorTestFire": "误码测试-发射", "errorTestReceive": "误码测试-接收", "exclusiveUser": "专属用户", "exit": "退出", "export": "导出数据", "exportConfig": "导出配置", "findUsbDevice": "查找USB设备", "firmwareVersion": "固件版本", "firstLevelArea": "隶属一级区域", "fixedLineNumber": "固话号码", "fixedLineNumberHelp": "0xx-xxxxxxx-xxxx", "flag": "标志", "forciblyOutNetworkAlarm": "强行脱网报警", "freqAndChDisplay": "频率+信道显示", "freqDisplay": "频率显示", "frequencyRange": "频率范围(MHz)", "frequentContacts": "常用联系人", "fullCall": "全呼", "fullCallPerm": "全呼权限", "gatewayId": "网关ID", "gatewayIp": "网关IP", "gatewayName": "网关名称", "gatewayPermission": "电话终端授权", "gatewayRule": "网关规则", "ge": "个", "generalDmr": "常规DMR终端", "generalSetting": "常规设置", "getLngLat": "从地图中心获取", "gpsAlarm": "定位故障报警", "gpsCardNo": "虚拟点卡号", "gpsRadius": "定位半径(m)", "gpsVirtualSet": "虚拟巡逻点设置", "grantUser": "授权用户", "groupCall": "组呼", "groupCallDialing": "组呼拨号", "groupCallMapping": "组呼映射", "groupHangTime": "组呼挂起时间({unit})", "groupName": "分组名称", "handmode": "手动模式", "hangTime": "挂起时间(ms)", "hangup": "挂断", "hasBeenDisListen": "已被禁听", "hasBeenDisSend": "已被禁发", "hasBeenRoaming": "已漫游", "hasEGAlarmAndLocate": "已紧急报警自动定位", "hasFixedGroupSD": "已固定组呼调度", "hasGpsAutoCtrlLocate": "已自动定位监控", "hasLevelGroupCallSD": "已级别组呼调度", "hasNetworkRoamingSD": "已漫游联网调度", "hasOffNetwork": "已脱网", "haveruntime": "单次耗时(分)", "high": "高", "highAndLowPowerSwitch": "高低功率切换", "highFrequency": "高频(MHz)", "idShownMinLen": "ID最小显示位数", "imageMapPoint": "图标点", "imbeSn": "声码器SN", "importConfig": "导入配置", "importExcel": "导入数据", "inCtrlAlarm": "入界监控报警", "inbox": "收件箱", "incomingCall": "来电提醒", "index": "序号", "initTransferTargetTree": "请选择转接目标", "insLine": "巡逻线路", "insPoints": "巡逻点", "insRules": "巡逻规则统计", "installLocation": "安装地址", "interPhoneWfPerm": "对讲机写频", "interfacePos": "接口位置", "internetGateway": "互联网关终端", "interphone": "对讲机", "ipSettingMode": "IP设置模式", "isGroupCall": "是否组呼", "join": "加入", "joinBlackList": "加入黑名单", "joinWhiteList": "加入白名单", "keepAdding": "继续添加", "keepSending": "不停的发送", "key01": "按键01 ({type})", "key02": "按键02 ({type})", "key03": "按键03 ({type})", "key04": "按键04 ({type})", "keyboardLock": "键盘锁", "km_h": "千米/时", "languageSettings": "语言设置", "languageType": "语言类型", "lastCheckDevice": "最后巡逻设备", "lastCheckTime": "最后巡逻时间", "lastCheckUser": "最后巡逻人员", "lastDataTime": "最后数据时间", "lat": "纬度", "latDif": "纬度差", "late": "晚到", "launchContact": "发射联系人", "leaveTOT": "离岗限时(分)", "ledIndication": "LED指示", "ledIndicator": "LED指示灯", "level": "级别", "lineName": "线路名称", "linePointTitle": "巡逻点管理", "linePointType": "类型", "lineTitle": "巡逻线路管理", "listenChannel": "监听信道", "listenGroup": "已收听组", "listenTime": "监听时间(秒)", "lngLat": "经纬度", "local2net": "本地通话视为联网通话", "localCall": "本地呼叫", "localEditRxGroup": "允许本地编辑接收组", "localName": "本机名称", "localNumber": "本机号码", "localPort": "本地端口", "locateCount": "定位次数", "locateCtrl": "定位监控", "locateSpacing": "定位间隔(秒)", "locateTime": "定位时间(秒)", "lockedDev": "锁机", "lockedStatus": "锁机状态", "lon": "经度", "lonDif": "经度差", "longPress": "长按", "longPressDuration": "长按持续时间(ms)", "lookGpsPointData": "查看虚拟巡逻点详细数据", "low": "低", "lowFrequency": "低频(MHz)", "mainZone": "一级区域 {id}", "manualDialing": "手动拨号", "mapDisplayName": "地图显示名", "mapMarker": "地图标记点", "mapMarkerH": "标记高度", "mapMarkerW": "标记宽度", "mapPointJumpTip": "地图中心已跳转至当前标记点,请点击首页查看", "mapPointType": "巡逻点类型", "mappingTarget": "对应目标", "markerSize": "标记大小", "maxLat": "最大纬度", "maxLon": "最大经度", "maxSpeakTime": "通话限时(秒)", "memberList": "成员列表", "menuHangTime": "菜单挂起时间(s)", "menuSettings": "菜单设置", "meshDevice": "MC-N终端", "meshGateway": "MC-N网关", "meshGatewayDevice": "Mesh网关终端", "mi_h": "英里/时", "mid": "中", "minLat": "最小纬度", "minLon": "最小经度", "minute": "分钟", "miss": "漏查", "missedCall": "未接呼叫", "mobile": "车载台", "mobileCtrl": "移动监控", "mobileCtrlAlarm": "移动监控报警", "mobileDevReqDevGps": "网络对讲终端请求终端位置信息", "mobileDevice": "网络对讲终端", "mobilePhoneNumber": "手机号码", "mobilePhoneNumberHelp": "1xxxxxxxxxx", "mobileTerminalType": "终端类型：{type}", "mode": "模式", "model": "型号", "monitorSwitch": "监听开/关", "moreLinePointHty": "更多巡逻记录", "moveDn": "下移", "moveUp": "上移", "msgcontentplaceholder": "请输入短信内容", "msglimit": "短信条数最多不能超过10条", "muteAll": "全部静音", "muteTone": "提示音静音", "name": "名称", "neglect": "忽略", "networkIp": "网络IP", "networkMask": "子网掩码", "networkService": "网络服务", "networkSetting": "网络设置", "networkSpeaking": "联网通话", "networkSwitch": "网络开关", "networkTimeSlot": "联网信道", "new": "新建", "newContact": "新建联系人", "newSmsMessage": "新建短信", "nmi_h": "海里/时", "no": "否", "noFunction": "无功能", "noMatchText": "无匹配数据", "nonCompacting": "不压缩", "nonStandard": "非标准", "none": "没有", "noneLevel": "无级", "notFoundGpsData": "没有找到该卡号的虚拟巡逻点数据", "notSendSmsData": "未确认短信", "notes": "备注", "nothing": "无", "numKey": "数字键 ({num})", "number": "编号", "occurUnderVoltageAlarm": "发生欠压报警", "off": "关", "offNetwork": "脱网", "offNetworkGroupCallHangTime": "脱网组呼挂起时间(ms)", "offNetworkSingleCallHangTime": "脱网单呼挂起时间(ms)", "offlineRepeater": "下线中继", "offlineSign": "脱网标志", "on": "开", "onOff": "开关", "oneLevel": "一级", "onlineRepeater": "在线中继", "openCenterCHCtrl": "开启切换信道", "openListenFn": "开启监听功能", "openMobileCtrl": "开启移动监控", "openOutCtrl": "开启出界监控", "openPeripheralFn": "开启外设功能", "openSchedulingFn": "开启调度功能", "openSentryCtrl": "开启岗哨监控", "opentInCtrl": "开启入界监控", "orgDMRID": "组呼DMRID", "orgFullName": "单位全称", "orgNoMapPointMsg": "当前单位暂无地图标记点,请先添加", "orgPointJumpTip": "地图中心已跳转至当前单位标记点,请点击首页查看", "orgShortName": "单位简称", "orgTitle": "单位管理", "orgsAddMapPointMsg": "勾选并成功添加将会跳转至地图标记点页面添加标记点", "outCtrlAlarm": "出界监控报警", "outbox": "发件箱", "outgoingCall": "呼出呼叫", "parentController": "上级控制器", "parentOrg": "所属单位", "parentOrgName": "上级单位", "parentZone": "上级区域", "passThroughMode": "直通模式", "patrolSystem": "巡逻系统", "penNo": "围栏号", "permissionConditions": "准许条件", "phoneBlackWhiteList": "电话黑白名单", "phoneBook": "电话簿", "phoneNoInputHelp": "电话号码输入说明", "picture": "图像", "playTrack": "轨迹回放", "plosive": "爆破音", "pocDevice": "POC终端", "pocDeviceManage": "POC终端配置", "pocFormErrorTip": "请填写完整POC终端信息", "pocTreeTitle": "请选择通讯录", "pointCount": "巡逻点数", "pointIndex": "点序号", "pointName": "巡逻点名称", "pointRfid": "巡逻点RFID", "pointSerialNo": "点编号", "pollingTimeSlice": "轮询时间片(s)", "popUpBlacklist": "拨出黑名单", "popUpWhitelist": "拨出白名单", "postFence": "岗哨围栏", "postName": "职位名称", "postTitle": "职位管理", "power": "功率", "powerLevel": "功率等级", "powerOnPwd": "开机密码", "powerSavingMode": "省电模式", "powerSwitch": "功率切换", "praviteHangTime": "单呼挂起时间({unit})", "preMadeSms": "预制短信", "predefinedPhoneBook": "预定义电话簿", "primaryAreaID": "一级区域ID", "priority": "优先级", "privelege": "权限设置", "privilegeDevice": "终端位置授权", "prochatDevice": "Prochat终端", "prochatDeviceAssociatedName": "关联的Prochat终端", "prochatDeviceList": "Prochat终端列表", "prochatDomain": "Prochat融合网关IP", "prochatGateway": "Prochat网关", "prochatGatewayConfig": "Prochat融合网关配置", "prochatGatewayDevice": "Prochat网关终端", "prochatNo": "Prochat融合网关号码", "prochatPassword": "密码", "prochatPort": "Prochat融合网关端口", "prochatUserNo": "用户号码", "programmingPwd": "编程密码", "query": "查询", "queryAllChannel": "查询全部信道", "queryNotSentSMS": "查询未确认短信", "queryRepeaterInfo": "查询中继信息", "querySentSMS": "查询短信历史", "querySetting": "查询设置", "quickDailEnable": "主界面快捷拨号", "readData": "读取", "readFromDevice": "从设备复制信道配置", "readRfidFormDev": "从设备读取RFID,并从下拉选框选择", "realOrg": "真实单位", "realplayer": "下载播放器", "receive": "接收", "receiveDevice": "收信设备", "receiveFrequency": "接收频率", "receiveGroup": "收信组", "receiveGroupList": "接收组列表", "receiveLowPowerPromptInterval": "接收低电提示间隔(s)", "receiveNumber": "@:dialog.receive {num}(MHz)", "receiveOnly": "只接收", "receivingAnalogSubtoneCode": "接收模拟亚音码", "receivingDigitalSubtoneCode": "接收数字亚音码", "receivingList": "接收列表", "receivingSubtoneType": "接收亚音类型", "recordCompRatio": "录音压缩比", "recordEnable": "录音使能", "recording": "录音", "records": "条记录", "refuse": "拒绝", "registCode": "注册码", "registerToSystem": "注册到系统", "rejectingStrangeCalls": "拒绝陌生呼叫", "remoteCtrlPwd": "远程控制界面密码", "remoteDeviceDisabled": "远程设备禁用", "remoteDeviceEnable": "远程设备启用", "remoteMonitor": "远程监听", "removeFenceAllCtrl": "取消围栏监控", "repeater": "中继", "repeaterId": "中继ID", "repeaterInfo": "中继信息", "repeaterName": "中继名称", "repeaterNumber": "中继号码", "repeaterPluginHangTime": "中继插话挂起时间", "repeaterTimeSlot": "中继时隙", "repeaterVirtualTerminal": "中继虚拟终端", "repeaterWfPerm": "中继写频", "repowerOnAndRestart": "重新上电重启中继", "reqDevice": "申请终端", "requiredRule": "此项必填", "reset": "重置", "responseTimeout": "应答超时(s)", "responseTimeout1": "应答超时(ms)", "restartRepeater": "重启中继", "restoreDefaultIPAddress": "恢复默认IP地址", "reverseSubtoneDigital": "反响亚音数码", "rgpsMode": "可靠定位传输", "roamInterface": "漫游界面", "rootDirList": "根目录列表", "rpgsModeInfo": "该功能开启后，若终端上传定位未得到系统的确认，则会自动保存在终端本地，等待终端与系统重新连接后自动上传。", "rssiThreshold": "RSSI阈值(dBm)", "ruleName": "规则名称", "ruleTarget": "规则目标", "ruleTitle": "巡逻规则管理", "runNotesTitle": "运行日志", "rxFrequency": "接收频率(MHz)", "rxGroup": "接收组", "rxGroupsSetting": "接收组设置", "salerId": "经销商ID", "satellitePosition": "卫星定位", "satellitePositionService": "卫星定位服务", "satellitePositionSetting": "卫星定位设置", "save": "保存", "savePenNo": "设备储存序号", "scanEnable": "扫描开关", "schedulingSt": "调度状态", "secondaryArea": "隶属二级区域", "secondaryAreaID": "二级区域ID", "selComputer": "从本机选择", "selImgPrompt": "只能上传jpg/png文件，且不超过100kb", "selectLogoPrefix": "图片大小为：", "selectLogoSize": "500x164，PNG透明底文件，", "selectLogoSuffix": "且不超过1M", "select": "请选择", "selectCallTarget": "请选择通话目标", "selectColor": "选择颜色", "selectImage": "选择图片", "selectListenGroup": "请选择收听组", "selectPoint": "选择巡逻点", "selectZone": "请选择信道所属区域", "selectZoneCh": "指定开机区域/信道", "sendCmdHistory": "发送命令历史", "sendCmdTitle": "发送命令", "sendCommand": "可发送命令", "sendCount": "发送次数", "sendGroup": "默认发射组", "sendMessages": "发送短信", "sendPreambleDuration": "发射前导码持续时间(ms)", "sendTime": "发送时间", "sendTimeLimiter": "发射限时器(s)", "sendedSmsData": "短信历史", "senderDevice": "发送设备", "sentinelRadius": "岗哨半径(米)", "sentryCtrlAlarm": "岗哨监控报警", "serialNo": "自编号", "serialNumber": "序列号", "serverAddress": "服务器地址", "serverPort": "服务器端口", "serverSetting": "服务器设置", "setReceivingGroup": "配置固定收听组", "settingSt": "设置状态", "setupPhoneInfo": "网关终端管理", "shortNumber": "短号", "shortNumberMapping": "电话网关短号映射", "shortPress": "短按", "shortestDistance": "最小距离(m)", "showDeviceNameDisplay": "待机显示设备名称", "showLevel": "显示级别", "silent": "静默", "simulation": "模拟", "simulcastController": "同播控制器", "simulcastRepeater": "同播中继", "simulcastSystem": "同播系统", "singleCall": "单呼", "singleCallConfirm": "单呼确认", "singleCallDialing": "单呼拨号", "singleCallMapping": "单呼映射", "sipDomain": "SIP域名或IP", "sipGateway": "SIP网关", "sipGatewayConfig": "SIP网关配置", "sipGatewayDevice": "SIP网关终端", "sipNo": "SIP号码", "sipPassword": "SIP密码", "sipPort": "SIP端口", "sipProtocolDevice": "sip协议终端", "sipProtocolDevicePassword": "sip协议终端密码", "sipProxyGateway": "是否在后台运行此SIP网关", "sipServerGateway": "SIP服务器网关", "sipServerGatewayConfig": "SIP服务器网关配置", "sipServerGatewayDomain": "公开的的SIP服务器域名或IP", "sipServerGatewayListenPort": "SIP服务器监听端口", "sipServerGatewayRTPPortRange": "SIP服务器RTP端口范围", "sipSoundLocale": "SIP提示音语言", "size": "最小发送距离(米)", "slotMode": "时隙模式", "sms": "短信", "smsContent": "短信内容", "smsNotification": "短信通知", "smsType": "短信类型", "sortValue": "排序", "soundTip": "提示音开/关", "sourceDevice": "源终端", "speakDevice": "通话设备", "speakPrompt": "按住F2/空格讲话", "speedUnit": "速度单位", "squelchCalibration": "静噪校准", "squelchLevel": "静噪等级", "squelchSwitch": "静噪切换", "stackingBlacklist": "拨入黑名单", "stackingWhitelist": "拨入白名单", "standard": "标准", "standardEnableAndMute": "标准开启,标准静音", "standbyInterface": "待机界面", "startCopy": "开始复制", "startSetting": "开始设置", "startTime": "开始时间", "starting": "启动", "staticEncryption": "静态加密", "status": "状态", "stopAlarmSound": "停止报警声音", "stopTOT": "停留限时(分)", "subAudio": "亚音频", "subZone": "二级区域 {id}", "subaudioSetting": "亚音频设置", "subtoneDigital": "亚音数码", "suspendTime": "挂起时间", "switchCh": "切换信道", "switchChannel": "切换信道", "switchTransmitPower": "切换发射功率", "systemCenter": "系统中心", "tagName": "标记名称", "tailCancellation": "尾音消除", "tailSelection": "尾音选择", "targetDevice": "目标终端", "telecontrol": "遥开/遥闭", "telephoneDevice": "电话设备", "telephoneGateway": "网关终端", "telephoneNo": "电话号码", "terminalAlias": "终端别名", "terminalLevel": "终端级别", "terminalName": "终端名称", "tertiaryAreaID": "三级区域ID", "testMachineSignalDuration": "测机信号时长({unit})", "testMachineSignalInterval": "测机信号间隔({unit})", "textImportant": "重要短信", "textInfo": "普通短信", "textMsg": "文本短信", "threeLevel": "三级", "timeSegment": "有效时间段", "timeSetting": "时间设置", "timeSlot": "时隙 {num}", "timeSlots": "时隙", "timeZone": "时区", "timeZoneHours": "时区小时", "timeZoneMinutes": "时区分钟", "timeZoneSet": "时区设置", "toneOrTip": "音调/提示", "totPwdUpdateDelay": "TOT密码更新延迟(s)", "tr925HighPower": "高功率--25W(车)/15W(背)", "tr925LowPower": "低功率--5W(车)/3W(背)", "tr925MediumPower": "中功率--10W(车)/6W(背)", "trailCtrl": "跟踪监控", "trailSpacing": "跟踪间隔(秒)", "transFrequency": "发射频率", "transTimeLimit": "发射限时", "transmitAnalogSubtoneCodes": "发射模拟亚音码", "transmitDigitalSubtoneCodes": "发射数字亚音码", "transmitSubtoneDigital": "发射亚音类型", "triggerCardReading": "按键触发读卡", "turnOn": "开启", "twoLevel": "二级", "txFrequency": "发射频率(MHz)", "txPower": "发射功率", "type": "类型", "uDiskMode": "U盘模式", "uDiskModePassword": "U盘模式密码", "unListenGroup": "未收听组", "unencrypted": "未加密", "update": "更新", "updateMapPoint": "更新地图标记点", "useDevice": "使用设备", "useUnit": "使用单位", "userLoginName": "登陆账号", "userLoginPass": "登陆密码", "userName": "用户名称", "userPhone": "联系电话", "userRfid": "RFID卡号", "userZone": "三级区域 {id}", "usersDataTitle": "用户管理", "usersPrivelegeSet": "用户权限设置", "vcController": "虚拟集群控制器", "vcRepeater": "虚拟集群中继", "version": "版本信息", "versusEnableAndMute": "与开启,标准静音", "versusEnableOrMute": "与开启,或静音", "viewContacts": "查看联系人", "vioAuto": "自动(不放音)", "vioPlay": "放音", "virOrg": "虚拟单位", "virtualCluster": "虚拟集群", "virtualClusterDevice": "虚拟集群对讲机", "virtualTimeSlot": "虚拟集群发射时隙", "vocalRule": "发声规则", "voiceControl": "声控", "voiceCryptoKey": "语音加密密钥", "voiceCryptoType": "语音加密类型", "voiceCtrl": "语音监控", "voiceDelay": "声控延迟(ms)", "voiceFrameRelayCheck": "语音帧中转校验", "voiceIndication": "语音指示", "voiceLevel": "声控等级(VOX)", "voicePrompt": "语音提示", "volumeDown": "音量减", "volumeUp": "音量加", "whiteList": "白名单", "workingSt": "工作状态", "workspaceAcrossBorders": "已工作区越界", "writeIn": "写入", "yes": "是"}, "dtable": {"all": "全部", "buttons": {"colvis": "显示列/隐藏列"}, "emptyTable": "没有任何数据", "info": "共_TOTAL_条记录", "infoEmpty": "没有记录", "infoFiltered": "(从_MAX_条记录中搜索)", "lengthMenu": "显示 _MENU_ 项", "loadingRecords": "加载中...", "paginate": {"first": "<span class='bf-icon gw-first'></span>", "last": "<span class='bf-icon gw-last'></span>", "next": "<span class='el-icon-caret-right'></span>", "previous": "<span class='el-icon-caret-left'></span>"}, "processing": "处理中...", "search": "<span class='el-icon-search'></span>", "zeroRecords": "没有匹配记录"}, "dynamicGroup": {"addMember": "新增成员", "alreadyJoined": "{member}已加入%{group}", "answeredExit": "已应答退出", "cannotModifyQuickTempGroup": "不能修改快捷临时组成员", "deleteDynamicGroupTips": "是否删除该动态组？", "device": "终端", "dynamicGroupMembers": "动态组成员", "dynamicGroupPermission": "动态组权限", "exitWithoutAnswer": "未应答退出", "expired": "已失效", "expiredDynamicGroup": "临时组或任务组已失效", "fast": "快捷", "fastDynamicGroupNoModify": "快捷临时组，不允许修改", "groupCall": "组呼", "groupCallDevice": "组呼下终端", "groupMemberUpperLimit": "组成员数量已达上限", "groupMembersLimit": "组成员数量超出上限", "groupName": "组名", "invalidTaskGroup": "任务组已失效", "isForceDelete": "是否强制删除？", "joinWithoutAnswer": "未应答加入", "jumpToNetworkCall": "是否跳转到联网通话?", "member": "成员", "memberDetails": "成员详情", "memberName": "成员名称", "memberOrg": "成员单位", "noDynamicGroupDmrId": "没有可用的动态组DMRID", "noGroupMembers": "未选择组成员", "noSuchDynamicGroup": "没有找到该动态组", "noUpdates": "没有成员变更，无需更新", "normal": "正常", "notFoundDbHandler": "无法查找到对应的数据库，请重新登录", "notFoundDynamicGroup": "没有查找到该动态组，请重试", "notFoundMemberInDynamicGroup": "无法在动态组中查找到该成员", "pcDeviceExitTaskGroup": "指挥坐席{pcDevice}已经退出{dynamicGroup}任务组", "pcDeviceJoinedTaskGroup": "指挥坐席{pcDevice}已经加入{dynamicGroup}任务组", "permDied": "无操作权限", "preempted": "被抢占", "removeMember": "移除成员", "repeatGroupName": "重复的动态组名", "taskGroup": "任务组", "taskGroupExpiredWithUpdate": "任务组已经失效，操作失败！", "tempGroup": "临时组", "tempGroupInvalidAndDelete": "无效后自动删除？", "title": "动态组", "updateFailedDetail": "更新失败成员详情", "updateGroupMembers": "更新动态组({group})成员"}, "error404": {"goBack": "返回上一页", "goHome": "返回首页", "notFoundPage": "访问的页面不存在..."}, "header": {"CN": "中文", "EGAlarmOnMapCenterTips": "紧急报警时在地图居中", "EN": "English", "FR": "Français", "InsOnMapTips": "巡逻时在地图上提示", "InspectionNotice": "巡逻时通知提示", "Korean": "韩文", "Russian": "俄文", "accountManager": "账号管理", "callingTips": "通话提示", "checkChannelListenGroup": "检查终端接收组配置", "clearCache": "重新加载", "detectNewUiTips": "检测到新版页面，是否切换？", "emergencyAlarmTips": "紧急报警提示", "fancytreeSortType": "列表树排序：终端排在单位前面", "fullScreen": "全屏切换", "language": "语言", "logo": "Logo图片", "logoName": "北峰电讯定位系统", "moveText": "滚动标题", "moveTitle": "北峰BF-8100 DMR数字智能集群指挥调度系统", "navBtn": "导航", "newDevOrCtrlData": "显示没登记的设备信息", "newPwd": "新密码", "newPwd2": "确认密码", "oldPwd": "旧密码", "onAndOffTips": "开、关机提示", "onMapDisplayLinePointName": "地图上显示巡逻点名称", "openEGAlarmSound": "紧急报警时播放声音", "orgList": "单位列表", "scrollMoveTitle": "标题是否滚动", "serverTitle": "服务器已连接", "setting": "设置信息", "showActivePointBattery": "有源点电量低时提示", "showCrossBorderAlarm": "显示越界报警信息", "showCtrlMarker": "地图上显示设备标记", "showCtrollerStats": "设备断开连接时提示", "showDispatchInfo": "显示调度信息提示", "showIotDeviceMarkers": "地图上显示物联网终端标记", "showOnlyLocateTerminals": "地图上只显示有效定位终端标记", "showOnlyOnlineTerminals": "地图上只显示在线终端标记", "showSmsResNotify": "显示终端收到短信通知", "siteTitle": "DMR智能集群指挥调度系统", "startAndEndWorkTips": "上、下班提示", "switchNewUi": "切换新版页面", "switchOldUi": "切换旧版页面", "switchOldUiTips": "是否切换到旧版页面?", "switchnewUiTips": "是否切换到新版页面？", "sysSet": "系统设置", "userName": "用户名", "userSet": "个人设置"}, "iot": {"acOff": "交流AC关", "acOn": "交流AC开", "devId": "终端ID", "duplicateID": "重复的ID", "energySavingLamps": "节能灯", "humidity": "湿度", "iotInspectDevice": "物联巡逻终端", "lastCmd": "最后指令", "normalClockIn": "正常打卡", "orgIdError": "所属单位数据错误", "receiveUnknownIotDevice": "收到未登记的物联终端数据", "smokeDetector": "烟感", "tempDetector": "温/湿度探测器", "tempReport": "温/湿度上报", "temperature": "温度", "terminal": "物联网终端", "thingsCard": "物卡"}, "iotDevHistory": {"antiFoldAlarm": "防拆报警", "cmdHex": "命令", "cmdTime": "命令时间", "emergency": "紧急报警", "heartbeat": "心跳", "lowPower": "低电报警", "overTemperatureAlarm": "超温域报警", "recvTime": "接收时间", "remove": "移动报警", "report": "打卡"}, "loginDlg": {"account": "系统登录", "adminPlatform": "管理平台客户端", "backLogin": "返回登录", "connected": "服务器已连接", "connecting": "正在连接服务器", "disconnect": "服务器连接失败", "dispatchPlatform": "调度平台客户端", "loading": "加载中...", "logging": "正在登录，请稍候", "login": "安全登录", "loginPassword": "输入账号密码", "loginSuccess": "登录成功", "loginTimeout": "登录超时，请重新登录", "loginWind": "登录窗口", "name": "输入账号名称", "noAdminAndDispatch": "无管理员或调度员权限", "noHasUser": "无此用户，请重新登录", "oldSession": "登录会话已过期，请用密码登录", "password": "密码", "passwordError": "密码错误，请重新输入", "platform": "选择平台", "remember": "记住账号", "system": "系统号", "upgradeServerTip": "当前服务器版本过低({currentServerVersion})，请升级到({latestServerVersion})版本！"}, "map": {"DIST": "距离", "E": "正东", "EN": "东北", "ES": "东南", "EbyN": "东偏北", "EbyS": "东偏南", "N": "正北", "NbyE": "北偏东", "NbyW": "北偏西", "S": "正南", "SbyE": "南偏东", "SbyW": "南偏西", "W": "正西", "WN": "西北", "WS": "西南", "WbyN": "西偏北", "WbyS": "西偏南", "clickMapGetCoordinates": "请点击地图获取坐标", "degrees": "度", "distances": "测距", "fastForward": "快进", "mapLevel": "地图级别:", "pased": "暂停", "play": "播放", "quitMapMsg": "已退出轨迹回放", "resetBearingToNorth": "将方位重置为北", "satellite": "卫星图", "selectCoordinates": "选择坐标", "selectCoordinatesTips": "请先激活选择坐标控件{iconEl}，再按下鼠标拖拽选择范围，点击选中的范围确认结果", "slowDown": "慢放", "stop": "结束", "streets": "街道图", "zoomIn": "放大", "zoomOut": "缩小"}, "msgbox": {"FailedReadSome": "文件{fileName}读取失败", "ScheduleStartTime": "开始调度时间", "ScheduleTargetChannel": "调度目标信道", "UsbServerConnectError": "USB终端服务器连接错误", "UsbServerConnectSuccess": "USB终端服务器连接成功", "UsbServerNotConnected": "USB终端服务器未连接", "addError": "添加失败", "addSuccess": "添加成功", "addToSql": "打开窗口添加新设备", "alarmSetSendOk": "报警设置命令发送成功", "allChannel": "所有信道", "alreadyFirst": "已经第一行了", "alreadyLast": "最后一行了", "areInspections": "正在巡逻", "areaResult": "区域查找结果", "arrivalTimes": "请设置到达时间", "associatedProchatDeviceNotSet": "未设置关联的Prochat终端", "batchSettingChannelFailed": "批量复制信道失败，请稍候再试", "batchSettingChannelSuccess": "批量复制信道成功", "bdNumberUnique": "号码不能重复", "bdUserNameUnique": "联系人名称不能重复", "being": "正在", "canNotDeleteProchatGatewayDevice": "不能删除Prochat网关终端", "canNotDeleteProchatUser": "不能删除Prochat网关用户", "canNotDeleteRootUserData": "不能删除超级用户数据", "canNotDeliveredByGroup": "该命令不支持按组下发", "canNotEditProchatGatewayDeviceDmrId": "不能编辑Prochat网关终端的DMRID", "canNotEditProchatGatewayDeviceType": "不能编辑Prochat网关终端的终端类型", "canNotEditProchatGatewayType": "不能更改Prochat网关设备类型", "canNotEditProchatUserSeIfId": "不能编辑Prochat网关用户的自编号", "canNotEditRootUserData": "不能编辑超级用户数据", "canNotEditSipServerGatewayType": "SIP服务器网关类型不能修改", "cancelAlarmCmd": "取消报警设置命令", "cancelEGChannelCmd": "取消紧急调度命令", "cancelGPSVirCmd": "取消设置虚拟巡逻点命令", "cancelGpsInsSet": "已取消设置虚拟巡逻点", "cancelMoveCtrlCmd": "取消移动监控命令", "cancelSchedule": "取消调度", "cancelSentinelCmd": "取消岗哨围栏命令", "cancelTrailCmd": "取消跟踪监控命令", "cancelVioCmd": "取消语音监控命令", "cannotInputChinese": "不能输入中文", "cannotSendCb08Cmd": "USB Connector未连接，不能语音监控", "cannotSetAsSubOrg": "上级单位不能设置为自己的下级单位", "cardNo": "卡号", "centerChannelCtrl": "切换信道命令发送成功", "channel": "信道", "channelBusy": "繁忙", "channelCtrlSendOk": "调度信道命令发送成功", "channelNotSetListenGroup": "{channel}信道没有配置接收组，无法正常调度通话", "character": "字符", "checkADevice": "请先选择要导入原始定位数据的设备", "clearAlarmOk": "解除报警成功", "clearEGAlarm": "解除紧急报警", "clearSendListenSt": "解除锁机控制", "configSuccess": "配置成功", "confirmAgainToDel": "请再次确认是否删除", "confirmGpsInsSet": "已启用虚拟巡逻点", "controllerGatewayUniqueDevice": "电话网关设备只能关联一个中继", "controllerGatewayUniquePhonePos": "一个电话接口只能关联一个电话网关", "ctrlData": "设备数据", "ctrlDisConnect": "设备已断开连接", "ctrollerFailureAlarm": "设备报警", "currentCanInput": "当前可输入", "currentRfidTime": "本次读卡时间", "currnetLocate": "当前位置", "dc01NotFoundLinePoint": "没有找到巡逻点数据，巡逻点卡号：{rfid}", "dc01UserDataNotFound": "没有找到用户数据，RFID卡号：{rfid}", "delError": "删除失败", "delLinePointError": "该巡逻点被巡逻线路所使用，请先删除巡逻线路", "delOutsidePermissions": "不能删除权限外的数据", "delSuccess": "删除成功", "deleteChannelFailed": "删除失败，不能删除当前信道", "deviceData": "设备数据", "deviceFault": "设备故障", "deviceModelError": "机型错误", "deviceNotReturnData": "终端没有回返数据", "deviceTerminateReadOpt": "终端终止读取操作", "disDelOrg": "该单位下的巡逻点被其他线路引用，请先删除巡逻线路", "disconnectDevice": "无法连接设备", "downloadFailed": "下载失败", "downloading": "正在下载", "dui": "对", "duplicateName": "名称重复，请重新输入", "emptyDMRID": "DMRID 不能为空", "emptyText": "暂无数据", "enableAlarmCmd": "启动报警设置命令", "enableEGChannelCmd": "启动紧急调度命令", "enableGPSVirCmd": "设置虚拟巡逻点命令", "enableInAndOut": "启用出入界监控命令", "enableInCtrlCmd": "启用入界监控命令", "enableMoveCtrlCmd": "启动移动监控命令", "enableOutCtrlCmd": "启用出界监控命令", "enableSentinelCmd": "启动岗哨围栏命令", "enableTrailCmd": "启动跟踪监控命令", "enableVioCmd": "启动语音监控命令", "endCall": "已结束通话", "enterCorrectDMRID": "请输入正确的DMRID", "enterCorrectLandlinePhoneNumber": "请输入正确的固话号码", "enterCorrectMobilePhoneNumber": "请输入正确的手机号码", "enterCorrectPhoneNumber": "请输入正确的电话号码", "example": "例子", "exportContacts": "导出的通讯录数据(单位、设备)，请在写频软件中导入使用！", "exportError": "导出数据失败", "exportSuccess": "导出成功", "fence": "号围栏", "fenceCmdSendOk": "电子围栏命令发送成功", "firstConnectTC918": "请先连接TC918终端或程序", "fixLastRfidTimeWarning": "上次读卡时间异常，是否修正为本次读卡时间？", "fixed16NumbersOrUppercaseLetters": "固定16个数字或大写字母", "getCtrlDataError": "获取设备数据失败，请重新登录", "getDevDataError": "获取设备数据失败，请重新登录", "getIotDataError": "获取物联网终端数据失败，请重新登录", "getLineDataError": "获取巡逻线路数据失败，请重新登录", "getLinePointDataError": "获取巡逻点数据失败，请重新登录", "getMapPointDataError": "获取标记点数据失败，请重新登录", "getOrgDataError": "获取单位数据失败，请重新登录", "getRuleDataError": "获取巡逻规则数据失败，请重新登录", "getUserDataError": "获取用户数据失败，请重新登录", "getUserTitleDataError": "获取职位数据失败，请重新登录", "gotNoRegisterCtrlData": "收到未登记的新设备数据", "gotNoRegisterDevData": "收到未登记的新终端设备数据", "gotRepeatCmd": "收到重复的命令", "gpsVirSendOk": "设置虚拟巡逻点命令发送成功", "handRoam": "手动漫游", "highFrequency": "频率过高", "importSuccess": "导入成功", "importValidJsonFile": "请导入有效的json格式文件", "inTheRange": "在范围内", "inputErrAndSeeHelpInfo": "输入错误，请查看帮助信息", "inputNotValid": "DMRID输入不合法", "insLineNotHasPoint": "巡逻线路有巡逻点被删除，请重新更新该线路", "insPointsCar": "巡逻卡", "intoAlarm": "入界报警", "intoGang": "入界回岗", "invalidIdentityInfo": "无效的身份信息", "invalidLngLat": "无效的经纬度", "ipAddress": "IP地址", "isClockIn": "正在上班打卡", "isPunchShift": "正在换班打卡", "lastData": "最后数据", "lastLocate": "最后定位", "lastRfidTime": "上次读卡时间", "licenceIsFull": "已达到授权的最大数量", "lineData": "巡逻线路数据", "linePointData": "巡逻点数据", "locateCmdSendOk": "定位监控命令发送成功", "locked": "锁机", "loginExpiredAndLoginAgain": "登录会话已失效，请重新登录!", "loginNameUnique": "登录账号重复", "loginNetwork": "入网登录", "loginSessionExpired": "登录会话已失效，请等待自动登录或重新登录", "lowFrequency": "频率过低", "mapPointData": "标记点数据", "matchAnyNumber": "匹配任意个任意数字", "matchArbitraryNo": "匹配一个任意数字", "maxLen": "最大可输入", "maxSelTime": "最大只能查询三个月数据", "minLen": "最小长度", "moveCtrlSendOk": "移动监控命令发送成功", "mustBe10Len": "密钥长度必须为10个", "mustBeHex": "必须是16进制数字", "mustBeHexChar": "必须是16进制字符", "mustIp": "必须是ip地址", "mustIpOrDomain": "必须为IP或者域名", "mustLength": "必须输入{len}个字符", "mustNumber": "必须是数字", "nameCannotRepeated": "名称不能重复", "newCard": "新卡", "nextTimeEffect": "本次设置下次查询生效!", "noDMRID": "没有dmrId,请先设置", "noRecords": "没有记录", "noResponseSatellitePositioningSwitchCommand": "没有回应卫星定位开关命令", "noTarget": "系统不存在此目标", "noTerminalQuotaAvailable": "没有可用的终端配额", "notDynamicGroupPerm": "没有动态组权限", "notEdit": "没有编辑数据权限！", "notEditSefl": "不能编辑自己的数据！", "notEditUserPerm": "没有权限编辑用户的权限数据", "notHasLinePoint": "巡逻线路没有巡逻点，请更新巡逻线路", "notHasOrg": "选择的所属单位不存在", "notIntercomWf": "没有对讲机写频权限", "notLinePoint": "没有该巡逻点数据", "notRepeaterWf": "没有中继写频权限", "notResAlarmCmd": "没有回应报警设置命令", "notResAreaCmd": "没有回应区域查找命令", "notResCenterChCtrl": "没有回应中心切换信道命令", "notResClearAlarmCmd": "没有回应解除报警命令", "notResEGChannelCmd": "没有回应紧急调度命令", "notResFenceCmd": "没有回应电子围栏命令", "notResGPSVirSet": "没有回应虚拟巡逻点命令", "notResLockDevCmd": "没有回应锁机命令", "notResMoveCtrlCmd": "没有回应移动监控命令", "notResPositionCmd": "没有回应定位监控命令", "notResPowerOnCmd": "没有回应开机命令", "notResSelectCmd": "没有回应查询锁机状态命令", "notResSentinelCmd": "没有回应岗哨围栏命令", "notResTextMsgCmd": "没有回应发送短信命令", "notResTrailCmd": "没有回应跟踪监控命令", "notResVioCmd": "没有回应语音监控命令", "notSendCmd": "没有发送命令权限！", "notSetNetworkCallAgent": "未设置联网通话坐席配置", "notSupportSatellitePositioning": "不支持卫星定位", "notVoipService": "没有获取到语音服务器信息，不能进行联网通话", "oldPwdError": "旧密码不正确", "onCalling": "通话中", "openEmergencySchedule": "开启紧急基站联网调度", "orgData": "单位数据", "orgHasOtherData": "该单位下已有数据，不能更改为虚拟单位", "orgNotHasDev": "该单位没有设备", "other": "其他", "outAlarm": "出界报警", "outGang": "出界离岗", "parentNotIsSelfOrSubUnit": "上级单位不能是自己或下级单位", "phoneBookNameNo": "重复的名称，请重新输入", "portLimit": "端口号不能超过65535", "postData": "职位数据", "poweredOff": "已关机", "poweredOn": "已开机", "processDataUpperLimitVal": "请输入更大的上限值!", "processMoreRecords": "是否需要处理更多数据？如不需要请取消，否则请输入数据上限条数。", "processMoreRecordsErrMsg": "输入的数据不合法!", "prochatArgsNotSet": "ProChat网关配置不正确", "prochatDeviceIsExisted": "此Prochat公网终端已存在", "prochatGatewayExist": "ProChat网关已存在", "programmingPwdError": "编程密码错误", "queryCtrlHistory": "查询设备在线历史记录", "queryFenceCmd": "查询电子围栏查询命令", "queryHistoryFail": "服务器断开连接,查询失败！", "querySatellitePositioningStatus": "查询卫星定位状态", "queryTrailCmd": "查询跟踪监控命令", "querying": "正在查询中...", "readAbnormal": "读取数据异常", "readDataFailed": "读取数据失败", "readDeviceInfoFailed": "读取设备信息失败", "readFileSuccessAndFaile": "读取文件成功：{success}个，读取失败文件：%{fail}个", "readIdentityInfoFailed": "读取身份信息失败", "readRecordListFailed": "读取录音文件列表失败", "readResourceVersionInfoFailed": "读取资源版本信息失败", "readSuccess": "读取成功", "reading": "读取", "receiveData": "接收数据", "receiveLocateInfo": "收到手动定位信息", "receivedSMS": "收到{sender}在%{sendTime}发送的短信", "repeatDMRID": "重复的DMRID，请重新输入", "repeatDevsName": "重复的设备名称，请重新输入", "repeatNo": "重复的自编号，请重新输入", "repeatOrgShortName": "单位简称重复，请重新输入", "repeatPointRfid": "重复的RFID，请重新输入", "repeatPostName": "职位名称重复，请重新输入", "repeatSipNo": "重复的SIP号码，请重新输入", "repeaterBusy": "中继信道繁忙", "repeaterParametersError": "参数有错误，请检查设置", "repeaterQueryFailed": "目标不存在", "repeaterVirtualDeviceRepeatDmrId": "中继虚拟对讲机DMRID重复", "repeaterVirtualDeviceRepeatSelfId": "中继虚拟对讲机名称重复", "repeaterWriteFail": "中继写频失败", "repeaterWriteSuccess": "中继写频成功，重启后生效", "repeaterWriteSuccessEffect": "中继写频成功，立即生效", "resAlarmSetCmd": "响应报警设置命令", "resCb09Cmd": "响应遥开遥闭命令", "resCenterChannelCtrl": "响应切换信道命令", "resChannelCtrlCmd": "响应信道调度命令", "resClearLockDev": "响应解除锁机命令", "resDevLockSt": "响应遥开/遥闭查询命令", "resDisListen": "响应禁听锁机命令", "resDisSend": "响应禁发锁机命令", "resDisSendListen": "响应禁听禁发锁机命令", "resFenceCtrlCmd": "响应电子围栏命令", "resGpsVirCmd": "响应虚拟巡逻点命令", "resLocateCmd": "响应定位监控命令", "resMoveCtrlCmd": "响应移动监控命令", "resSentinelCtrlCmd": "响应岗哨围栏命令", "resTrailCtrlCmd": "响应跟踪监控命令", "resVioCtrlCmd": "响应语音监控命令", "resetPwdError": "两次输入的密码不一致", "resolveDmrId": "请输入DMRID，十六进制以0x开头", "resolveDmrIdErr": "DMRID位数不正确", "resolveDmrIdFailed": "解析DMRID失败，请输入正确的DMRID", "resolveDmrIdMsg": "请输入8位DMRID", "resolveDmrIdTitle": "点击可以解析DMRID", "respond": "响应", "ruleData": "巡逻规则数据", "ruleDateAlert": "有效日期的结束时间不能小于开始时间", "salerId": "系统没有配置经销商ID", "satellitePositioningTurnedOff": "已关闭卫星定位", "satellitePositioningTurnedOn": "已开启卫星定位", "scheduleModal1": "固定四组呼调度", "scheduleModal2": "固定三组呼调度", "scheduleModal3": "固定二组呼调度", "scheduleModal4": "信道群呼调度", "scheduleModal5": "基站群呼调度", "scheduleModal6": "3级(三级机)组呼调度", "scheduleModal7": "2、3级(二级机)组呼调度", "scheduleModal8": "3级(二级机)组呼调度", "scheduleModal9": "2级(二级机)组呼调度", "scheduleModalA": "1、2级(一级机)组呼调度", "scheduleModalB": "2级(一级机)组呼调度", "scheduleModalC": "动态组呼调度", "scheduleModalD": "1级组呼调度", "scheduleModalE": "系统全呼调度", "scheduleModalF": "基站联网调度", "selAlarmCmd": "查询报警设置命令", "selError": "查询失败", "selGPSVirCmd": "查询虚拟巡逻点设置状态", "selImgError": "请选择小于 100kb 的图片", "selLinePoint": "请选择巡逻点", "selLockDevSt": "查询锁机状态", "selMoveCtrlCmd": "查询移动监控命令", "selSentinelCmd": "查询岗哨围栏命令", "selSuccess": "查询成功", "selTimeError": "时间设定有误，请重新设定", "selectGpsPoint": "请选择虚拟巡逻点", "selectLngLat": "请设置经纬度", "selectTarget": "请选择发送命令目标", "selectTime": "请选择时间", "sendAreaCmd": "发送区域查找命令", "sendClearAlarmCmd": "发送解除报警命令", "sendError": "命令发送失败", "sendPositionCmd": "发送定位监控命令", "sendSuccess": "命令已发送", "sendTextMsgCmd": "发送短信通知命令", "sentinelCtrlSendOk": "岗哨围栏命令发送成功", "serverReconnect": "服务器已重启，请刷新再试", "serverRestartAndConnectFailed": "与服务器断开连接，自动连接失败，请重新登录", "setDisListenCmd": "设置禁听锁机", "setDisSLCmd": "禁发禁听锁机", "setDisSendCmd": "设置禁发锁机", "setPriPrompt": "请先确定或取消权限设置", "setupListenGroupLimit": "每个信道最多设置{limit}个接收组", "shortNoRepeated": "短号不能重复", "showActivePointAlert": "有源点电量过低", "sipArgsNotSet": "SIP网关配置不正确", "sipProtocolDevicePasswordIsNull": "sip协议终端的密码不能为空", "sipServerArgsNotSet": "SIP服务器配置不正确", "sipServerGatewayExist": "SIP服务器网关已存在", "slotlimit": "信道为1时，不能删除", "smsLengthInput": "@:msgbox.smsMaxLengthInput，@:msgbox.currentCanInput{curLen}@:msgbox.character", "smsMaxLengthInput": "@:msgbox.maxLen{len}@:msgbox.character", "smsNotEmpty": "短信内容不能为空", "softwareDownloadFailed": "@:msgbox.downloadFailed，无法找到软件", "sortAdvice": "建议以10为单位进行排序", "startMove": "开始移动", "startReading": "开始读取数据", "startWriting": "开始写入数据", "stopMove": "停止移动", "success": "成功", "superAdminWarning": "系统默认单位root下的用户账号不允许发送命令，请使用相关单位下的用户账号！", "switchRepeaterChannelFailed": "切换中继信道失败", "switchRepeaterChannelSuccess": "切换中继信道成功", "switchRepeaterFrequencyFailed": "切换中继频率失败", "switchRepeaterFrequencySuccess": "切换中继频率成功", "symbolDescription": "符号说明", "sysIdError": "系统ID号不对", "sysSetError": "系统设置失败", "sysSetSuccess": "系统设置成功", "targetInCalling": "目标在通话中", "targetNotLogin": "目标未登录", "targetNotOnline": "目标不在线", "targetNotSelf": "通话目标不能为自己", "telephoneNotSetChannelData": "{type}类型不需要设置信道数据", "theCardInfoOutDate": "本次读卡信息已过时！上次读卡时间：{lastTime}，本次读卡时间：%{thisTime}", "timeEmpty": "时间不能为空", "trailCtrlCmdOk": "跟踪监控命令发送成功", "tryAgainQueryChannelData": "网络异常，有部分信道数据无法接收，请稍候再试", "turnOffSatellitePositioning": "关闭卫星定位", "turnOnSatellitePositioning": "打开卫星定位", "unInTheRange": "不在范围内", "unLocked": "解除锁机", "unableDeleteRepeaterVirtualDevice": "无法删除中继虚拟对讲机，请稍候重试", "unableRequestTerminalQuota": "无法请求终端配额", "unknownCmd": "未知命令", "upError": "更新失败", "upLoadLocateInfo": "上传定位信息", "upSuccess": "更新成功", "updataClientLogo": "更新系统Logo图片", "updataClientTitle": "更新系统滚动标题", "updateDeviceChannelSuccess": "更新信道配置成功", "updateLineDetailFaile": "更新线路详情表失败", "updatePointType": "巡逻点已更新类型，请重新设置!", "updateUsbServerVersion": "请更新USB-Connector版本，最低版本：{required}，当前版本：%{curVersion}", "upgradeRepeaterVirtualDeviceFailed": "更新中继虚拟终端失败", "upgradeSipGatewayDeviceFailed": "更新SIP网关终端失败", "uploadTip": "只能上传{type}文件，且不超过%{size}kb", "upperLimit": "数据已达上限", "usbDeviceHasBeenOut": "USB设备已经拔出", "usbDeviceInsertSuccess": "USB设备插入成功", "useInRule": "删除失败，该线路被巡逻规则引用，请先删除规则", "userData": "用户数据", "userIDCard": "身份卡", "validDomain": "请输入正确的域名", "validFileSize": "请选择小于100k的文件", "validHost": "请输入正确的域名或IP", "validIp": "请输入正确的IP", "validMaxNumber": "请输入比最小值大的数字", "validMinNumber": "请输入比最大值小的数字", "vioCtrlSendOk": "语音监控命令发送成功", "writeActiveRFIDConfigFailed": "写入有源RFID配置失败", "writeAddressBookFailed": "写入通讯录失败", "writeAddressBookGroupFailed": "写入通讯录群组失败", "writeAlarmConfigFailed": "写入警报配置数据失败", "writeAnalogEmergencyAlertFailed": "写入模拟紧急警报失败", "writeAttachmentSettingsFailed": "写入附件数据失败", "writeBluetoothDataFailed": "写入蓝牙设置数据失败", "writeChannelDataFailed": "写入信道数据失败", "writeDeySettingsFailed": "写入按键设置数据失败", "writeDigitalAlarmFailed": "写入数字紧急报警数据失败", "writeDmrBasicConfigFailed": "写入DMR基础配置失败", "writeEmergencyAlarmConfigFailed": "写入紧急报警配置失败", "writeEncryptConfigFailed": "写入加密配置数据失败", "writeEncryptKeyFailed": "写入密钥数据失败", "writeEncryptionAES256KeyFailed": "写入高级密钥AES256数据失败", "writeEncryptionARC4KeyFailed": "写入高级密钥ARC4数据失败", "writeEncryptionARSKeyFailed": "写入高级密钥AES数据失败", "writeGPIODataFailed": "写入GPIO数据失败", "writeGpsDataFailed": "写入卫星定位数据失败", "writeInFail": "写入数据失败", "writeInSuccess": "写入成功", "writeLevel1ZoneFailed": "写入一级区域数据失败", "writeLevel2ZoneFailed": "写入二级区域数据失败", "writeLevel3ZoneFailed": "写入三级区域数据失败", "writeMenuFailed": "写入菜单数据失败", "writePatrolSystemConfigFailed": "写入巡逻系统配置失败", "writePhoneBookFailed": "写入电话簿数据失败", "writeProgrammingPwdFailed": "写入编程密码失败", "writeReadPasswordFailed": "写入编程读密码失败", "writeReceivingGroupFailed": "写入接收组失败", "writeRegularSettingsFailed": "写入常规设置失败", "writeRescueAndSOSChannelFailed": "写入救援/求救信道失败", "writeRescueAndSOSConfigFailed": "写入救援/求救配置失败", "writeRoamConfigFailed": "写入漫游配置数据失败", "writeRoamListFailed": "写入漫游列表数据失败", "writeSMSFailed": "写入短信数据失败", "writeScanConfigFailed": "写入扫描配置数据失败", "writeScanListFailed": "写入扫描列表数据失败", "writeSignalingSystemFailed": "写入信令系统数据失败", "writeSiteInfoFailed": "写入站点信息失败", "writeTraceMonitorConfigFailed": "写入跟踪监控配置失败", "writeUiConfigFailed": "写入UI配置失败", "writeUpsideDownConfigFailed": "写入倒放配置失败", "writeValidChannelFailed": "写入有效信道数据失败", "writeVirtualClusterFailed": "写入虚拟集群数据失败", "writeWorkAloneConfigFailed": "写入单独工作配置失败", "writeWritePasswordFailed": "写入编程写密码失败", "writeZoneDataFailed": "写入区域数据失败"}, "nav": {"BFWebsite": "北峰官网", "GPSpathHistory": "定位轨迹历史", "InspectionHistory": "巡逻历史", "InspectionRules": "巡逻规则", "activePatrolPointAlarm": "有源巡逻点报警历史", "activePointLowVoltageAlarm": "有源点低压报警", "alarmHistory": "报警历史", "authorization": "授权信息", "baseStationSchedule": "基站调度", "clientVersion": "客户端版本", "command": "命令", "contacts": "导出通讯录", "crudHistory": "操作历史记录", "ctrlData": "设备管理", "data": "数据", "enquiry": "查询", "help": "帮助", "helpAbout": "关于", "helpContent": "系统文档", "home": "首页", "interphoneWriteFrequency": "对讲机写频", "iotDeviceHistory": "物联网终端历史", "leadingInGpsData": "导入原始定位数据", "lineData": "巡逻线路", "linePointData": "巡逻点管理", "mapPointData": "地图标记点", "orgData": "单位管理", "postData": "职位管理", "readerCardHistory": "交班历史", "relatedSoftware": "相关软件", "repeaterWriteFrequency": "中继写频", "runNotes": "运行日志", "sendCommand": "发送命令", "serverVersion": "服务器版本", "smsHistory": "发送短信历史", "soundHistory": "录音历史", "startRuningTime": "运行时间", "switchHistory": "开关机历史", "systemLog": "系统日志", "userData": "用户管理", "version": "版本信息", "versionBuildTime": "构建时间", "versionGitTag": "<PERSON><PERSON><PERSON>", "phoneManage": "电话管理", "patrolManage": "巡逻管理", "iotManage": "物联管理", "otherOperations": "其他操作", "phoneGatewayMapping": "电话网关短号映射", "phoneBlackWhiteList": "电话黑白名单", "phoneDeviceAuth": "电话终端授权", "predefinedPhoneBook": "预定义电话簿", "patrolPointManage": "巡逻点管理", "patrolRouteManage": "巡逻路线管理", "patrolRuleManage": "巡逻规则管理", "runLog": "运行日志", "versionInfo": "版本信息", "authInfo": "授权信息", "logoSetting": "LOGO设置", "scrollTitleSetting": "滚动标题设置", "passwordChange": "账号密码修改", "clientDocs": "管理客户端使用文档", "controllerHistoryEvent": "设备历史事件", "GisApplication": "GIS应用", "CommunicationDispatch": "通信调度", "DataApplication": "数据应用", "AdminPlatform": "管理平台"}, "operations": {"Add": "增加", "Delete": "删除", "Insert": "插入", "PUpdate": "修改(部分参数)", "Update": "修改", "db_base_station": "基站数据", "db_controller": "控制器数据", "db_controller_gateway_manage": "电话网关设备关系管理", "db_device": "对讲机设备", "db_device_channel_zone": "终端信道区域数据", "db_device_power_onoff": "开关机数据表", "db_device_register_info": "对讲机注册信息", "db_dynamic_group_detail": "动态组成员", "db_image": "用户图片数据", "db_iot_device": "物联网终端", "db_line_detail": "巡逻线路详情", "db_line_master": "巡逻线路", "db_line_point": "巡逻线路点", "db_map_point": "地图标志", "db_org": "单位数据", "db_org_dynamic_group": "动态组", "db_phone_gateway_filter": "电话黑白名单", "db_phone_gateway_permission": "电话网关使用授权", "db_phone_no_list": "预定义电话号码本", "db_phone_short_no": "电话网关短号", "db_rfid_rule_master": "巡逻规则表", "db_sys_config": "系统设置", "db_user": "用户数据", "db_user_privelege": "用户权限（间接操作）", "db_user_session_id": "用户会话ID", "db_user_title": "用户职称"}, "repeaterStatus": {"ant": "天线", "antValue": "驻波值", "fan": "风扇", "gps": "GPS", "gpsStatus": "GPS状态", "notInstalled": "未安装", "notSynced": "未同步", "repeaterStatus": "中继状态", "rxPll": "接收", "signalInterference": "信号干扰", "synced": "已同步", "temperature": "温度", "thereIsInterference": "有干扰", "txPll": "发射", "updateTime": "更新时间", "voltage": "电压"}, "repeaterWriteFreq": {"abnormal": "异常", "hasLocate": "已定位", "locked": "已锁定", "normal": "正常", "notDetected": "未检测到", "notLocate": "未定位", "notLocked": "未锁定", "repeaterDevice": "中继设备", "repeaterModel": "中继型号", "state": {"antStatus": "天线状态", "fanStatus": "风扇状态", "freeRelay": "是否自由中继", "gpsStatus": "GPS同步状态", "pllStatus": "PLL锁定状态", "rxStatus": "接收状态", "sessionSlot1": "时隙1会话状态", "sessionSlot2": "时隙2会话状态", "tempStatus": "温度状态", "txStatus": "发射状态", "volStatus": "电压状态"}, "stateInfo": "状态信息", "tooHigh": "过高", "tooLow": "过低"}, "software": {"recordingPlayer": "录音播放器", "refresh": "刷新", "refreshFailed": "刷新失败", "refreshSuccess": "刷新成功", "usbTerminalServer": "USB终端服务器"}, "syncCenter": {"controller": "已同步设备数据", "controllerGateway": "已同步设备网关终端", "device": "已同步终端数据", "lineDetail": "已同步巡逻线路详情数据", "lineMaster": "已同步巡逻线路数据", "linePoint": "已同步巡逻点数据", "mapPoint": "已同步地图标记点", "org": "已同步单位数据", "orgDeleteAndLogout": "您所在的单位已被删除，即将退出登录!", "phoneGatewayFilter": "已同步电话黑白名单", "phoneGatewayPermission": "已同步电话终端授权", "phoneNoList": "已同步预定义电话簿", "phoneShortNo": "已同步电话网关短号", "ruleMaster": "已同步巡逻规则数据", "user": "已同步用户数据", "userTitle": "已同步职位数据", "warning": "警告"}, "tree": {"collapseAll": "全部折叠", "deselectAll": "全不选", "displayAllDev": "全部显示", "expandAll": "全部展开", "filter": "请输入搜索内容", "online": "显示在线", "quickCall": "快速呼叫", "selectAll": "全选", "status": "设备状态"}, "writeFreq": {"BUSYLineOutputEffectiveLevel": "BUSY线输出有效电平", "ChannelAndVolume": "信道和音量", "DefaultGroupCall": "默认组呼", "Exhale": "呼出", "GMTStandard": "GMT标准", "PTTYLineOutputEffectiveLevel": "PTT线输出有效电平", "SVTSiteInfo": "SVT站点信息", "TDMAThroughMode": "TDMA直通模式", "aLocateFunc": "A(倒放+单独工作报警)", "activeSiteEnable": "活动站点搜索", "activeSiteRoamingEnable": "主动站点漫游使能", "activeSiteSearch": "活动站点搜索", "activeSiteSearchTiming": "活动站点搜索计时(秒)", "addArea": "添加区域", "addChannel": "添加信道", "addressBookGroup": "通讯录群组", "addressGroup": {"selectedContact": "已选联系人", "ungroupedContact": "未分组联系人"}, "advanced": "高级", "advancedEncryptionAES256": "高级加密 AES256", "advancedEncryptionARC4": "高级加密 ARC4", "advancedEncryptionARS": "高级加密 AES", "aes256List": "AES256列表", "airAuthKey": "空中鉴权密钥", "airEncryption": "空口加密", "alarmAndWhistle": "警报鸣笛", "alarmAutoSendGps": "报警自动发送GPS消息", "alarmSetting": "警报设置", "alarmSquelchMode": "报警静噪模式", "alarmTone": "报警提示音", "alias": "别名", "aliasDisp": "别名显示", "aliasEdit": "别名编辑", "aliasMenu": "别名菜单", "allChannelDetection": "所有信道检测", "allIndicators": "所有指示灯", "allow": "允许", "allowDeleteAllRecord": "允许擦除录音文件", "allowErasingDevice": "允许擦除设备", "allowShake": "允许振动", "allowedSelfDestruct": "允许自毁", "always": "始终", "analogAlert": "模拟警报", "analogAllowed": "模拟允许", "analogCallHangTime": "模拟呼叫挂起时间(秒)", "analogChannel": "模拟信道", "analogCompatibleDigital": "模拟兼容数字", "analogEmergencyAlarm": "模拟紧急警报", "analogEmergencyAlertSystem": "模拟紧急警报系统", "arc4List": "ARC4列表", "areaId": "区域ID", "areaList": "区域列表", "areaNoCannotRepeated": "同一区域下的序号不能重复", "armAlarmKey": "肩咪警报", "authentication": "鉴权", "authenticationSecretKey": "鉴权密钥", "auto": "自动", "autoBackLightTime": "背光灯自动时间", "autoEmergencyCall": "自动紧急呼叫", "autoKeyboardLock": "自动键盘锁", "autoKeyboardLockDelayTime": "自动键盘锁延迟开启时间", "autoRoamSearchTime": "自动漫游搜索时间(秒)", "autoRoaming": "自动漫游", "autoRoamingSearchInterval": "自动漫游搜索间隔(秒)", "autoScanning": "自动扫描", "autoSiteSearchTimer": "自动搜索计时器", "availableChannel": "可用信道", "back2Back": "背靠背", "back2BackMode": "背靠背转发模式", "backToBackEnable": "背靠背开关", "backgroundPromptTone": "背景提示音", "backlight": "背光灯", "backlightSettings": "背光灯设置", "backwardsTriggered": "倒放触发方式", "base": "基础", "baseSettings": "基础设置", "basedOnChannelDefaultGroupCall": "基于信道默认组呼", "batchDownload": "批量下载", "batteryCharge": "电池电量", "batteryInfo": "电池信息", "bdsLocate": "北斗定位", "beatFreq": "拍频", "beidou": "北斗", "bluetooth": "蓝牙", "bluetoothAudio": "蓝牙音频", "bluetoothConnectManagement": "蓝牙连接管理", "bluetoothFunc": "蓝牙功能", "bluetoothInfo": "蓝牙信息", "bluetoothMode": "蓝牙模式", "bluetoothOption": "蓝牙选配", "bluetoothPTTKeep": "蓝牙PTT保持", "bluetoothRecordingChooseOne": "蓝牙选配和录音选配只能选择一项", "bluetoothSettings": "蓝牙设置", "bluetoothSwitch": "蓝牙开关", "bootInterfaceDisp": "开机界面显示", "broadBand": "宽带", "busyChannelLock": "繁忙信道锁定", "button": "按键", "buttonTone": "按键提示音", "callDir": "语音方向", "callDirectionEnable": "呼叫方位信息", "callDisplayMode": "呼叫显示模式", "callEmissionPermitConditions": "呼叫中发射准许条件", "callHangsLed": "呼叫挂起LED", "callMode": "呼叫模式", "callOut": "呼出", "callOutTone": "呼出提示音", "callPrompt": "呼叫提示提示音", "callPromptTimes": "呼叫提示次数", "callToneDecode": "呼叫提示解码", "callToneVibration": "呼叫提示振动", "callingEndTone": "呼叫结束音", "carrier": "载波", "carrierSquelchLevel": "静噪等级", "cdcss": "亚音数码", "cdcssInvert": "反向亚音数码", "chAreaChannelAsScanList": "信道区域信道作为扫描列表", "chTimeSlotCalibrator": "信道时隙校准器", "channelBroadcastSound": "信道播报音", "channelConfig": "信道配置", "channelDataFormValidate": "信道数据验证失败", "channelLock": "信道锁定", "channelNotSetArea": "有信道未设置区域", "channelNumber": "信道数量", "chooseAnnouncer": "选择播音人", "clearRecord": "记录清空", "clearSms": "短信清空", "closePosition": "关闭定位", "companding": "压扩", "compandingEnable": "压扩使能", "configName": "列表名称", "confirmedDataSingleCall": "已确认数据单呼", "connectionTimes": "连接次数", "contactAlias": "联系人别名", "contactAliasAndId": "联系人别名和ID", "contactDelete": "联系人删除", "contactGroup": "联系人群组", "contactId": "联系人ID", "containedChannel": "包含信道", "controlBusiness": "控制业务", "controlCenter": "控制中心", "correctChDataTip": "{name} 下辖信道数据有错误，请先修正", "ctcss": "亚音频", "ctcssCdcss": "亚音", "currentSlot": "当前时隙", "currentVolume": "当前音量", "customKey": "按键 {name}", "dataCallConfirm": "数据呼叫确认", "dataCompression": "数据压缩", "dateTime": {"date": "日期", "hours": "小时", "minutes": "分钟", "month": "月份", "year": "年份"}, "deEmphasisAndPreEmphasis": "去加重和预加重", "decoding": "解码", "defaultChannel": {"ch1": "预设信道1", "ch2": "预设信道2", "ch3": "预设信道3", "ch4": "预设信道4"}, "defaultKeyboardInput": "默认键盘输入", "defaultKnobFunc": "默认旋钮功能", "defaultPriCall": "默认单呼", "defaultTxChNetwork": "默认发射信道网络", "deleteGroup": "删除群组", "denoise": "降噪", "denoiseEnable": "降噪使能", "device": "设备", "deviceDetectDecode": "对讲机检测解码", "deviceRemoteDeadDecode": "设备遥毙解码", "deviceRemoteDestroy": "设备遥毁", "deviceStun": "对讲机遥晕", "deviceWokeUp": "对讲机遥醒", "digitalAlarmFormValidate": "数字报警数据验证失败", "digitalAlert": "数字警报", "digitalAllowed": "数字允许", "digitalAnalogChannel": "数模信道", "digitalChannel": "数字信道", "digitalCompatibleAnalog": "数字兼容模拟", "digitalEmergencyAlarm": "数字紧急警报", "digitalEmergencyAlertSystem": "数字紧急警报系统", "digitalMode": "数字模式", "disable": "禁用", "displayName": "显示名字", "displayNameAndNumber": "显示名字和号码", "displayNameNumber": "显示名字或别名", "displayNumber": "显示号码", "dmrBaseSettings": "DMR基础设置", "dualTimeSlot": "是否双时隙", "duplex": "双工", "duplexMode": "双工模式", "dynamicKey": "动态密钥", "early": "早", "edit": "编辑", "editContact": "联系人编辑", "editGroup": "编辑群组", "editList": "编辑列表", "emergencyAlarmAndCall": "紧急报警和呼叫", "emergencyAlarmAndVoice": "紧急报警和语音", "emergencyAlarmConfirm": "紧急警报确认", "emergencyAlarmExitDuration": "紧急警报长按退出时间", "emergencyAlarmIndication": "紧急警报指示", "emergencyAlarmSystem": "紧急警报系统", "emergencyAlertDuration": "紧急警报持续时间", "emergencyAlertMessage": "紧急警报报文", "emergencyAlertSystem": "数字/模拟紧急警报系统", "emergencyAlertTimes": "紧急警报次数", "emergencyCallAlert": "紧急呼叫提示", "emergencyCallTimes": "紧急呼叫次数", "emergencySystem": "应急系统", "emittingLed": "发射指示灯", "emphasis": "加重", "enable": "启用", "enableAuth": "启用鉴权", "encoding": "编码", "encryptType": "加密类型", "encryptedList": "加密列表", "encryption": "加密", "encryptionAlgorithm": "加密算法", "encryptionConfig": "加密配置", "encryptionKey": "加密密钥", "enhancedXor": "增强异或", "entryDelay": "进入延时(秒)", "exitDelay": "退出延时(秒)", "femaleVoice1": "女声1", "femaleVoice2": "女声2", "fieldStrengthInfo": "场强信息", "fileName": "文件名", "firstChoice": "首选", "firstPriorityChannel": "第一优先信道", "flashlightTime": "间隔性闪灯时间(ms)", "followMainSiteSettings": "跟随主站点设置", "forbid": "禁止", "forward": "转发", "freqOffset": "频率偏移(MHz)", "freqRepeated": "频率重复", "frequencyRangeError": "频率范围错误", "fullList": "列表已满，添加失败！", "gLocateFunc": "G(定位功能)", "galileo": "伽利略", "generalSettingsFormValidate": "常规设置数据验证失败", "getRecordList": "获取录音列表", "girlVoice": "女童声", "glonass": "格洛纳斯", "gpioSettings": "GPIO", "gpsAndBeidou": "GPS+北斗", "gpsEnable": "开启定位功能", "gpsLocate": "卫星定位", "gpsMode": "模式", "gpsUpdateTime": "卫星定位更新时间(秒)", "groupCallTone": "组呼提示音", "groupJoinOrExit": "加入或退出群组", "groupManagement": "群组管理", "highLevel": "高电平", "highPerformanceMode": "高性能模式", "highPowerSosTime": " 高电量求救发送间隔(min)", "hourDiff": "时钟差数", "idAlreadyExits": "该ID已存在，请重新输入", "imagePreview": "图片预览", "inOutNetworkTone": "网内与网外提示音", "inbound": "呼入", "incomingCall": "已接呼入", "independentSettings": "独立设置", "indicationTones": "指示音", "indicatorSettings": "指示灯设置", "interphoneActivation": "对讲机激活", "interphoneConfig": "对讲机配置", "interphoneDetection": "对讲机检测", "interphoneInfo": "对讲机信息", "interphoneRemoteKill": "对讲机遥毙", "intervalHonkingTime": "间隔性鸣笛时间(s)", "ipSiteConnection": "IP站点连接", "key01": "按键 01", "key02": "按键 02", "keyList": "密钥列表", "keyP": "按键P", "keyboardHangTime": "键盘背光挂起时间(s)", "keyboardLock": "键盘锁", "keys": "按键{key}", "lLocateFunc": "L(录音功能)", "langEnv": "语言环境", "lastActiveChannel": "上次活动信道", "lastActivityChannel": "最后活动信道", "late": "晚", "lcdHangTime": "液晶背光挂起时间(s)", "lgLocateFunc": "LG(定位+录音)", "localCall": "本地呼叫", "localEmergencyAlert": "本地紧急警报", "localEmergencyHonk": "本地紧急鸣笛", "locateFunc": "定位功能", "locateMode": "定位模式", "locationEnable": "定位开关", "locationSystem": "定位制式", "lockKeys": {"backKey": "返回键", "channelKey": "信道旋钮", "confirmKey": "确认键", "dialKey": "拨号键", "downKey": "方向下键", "f1key": "F1键", "f2key": "F2键", "f3key": "F3键", "f4key": "F4键", "f5key": "F5键", "func1Key": "功能1键", "func2Key": "功能2键", "knobKey": "旋钮键", "leftKey": "方向左键", "offKey": "摘机键", "onHookKey": "挂机键", "orangeKey": "橙色键", "p1Key": "P1键", "p2Key": "P2键", "pttKey": "PTT", "rightKey": "方向右键", "upKey": "方向上键", "volumeKey": "音量旋钮"}, "longPressNumCallTable": "长按数字键盘功能呼叫", "lookOver": "查看", "lowBatteryLed": "电池低电量LED", "lowLevel": "低电平", "lowPowerSosTime": "低电量求救发送间隔(min)", "lowVoltageAlarmTone": "低压告警提示音", "maleVoice1": "男声1", "maleVoice2": "男声2", "manualQuickCallType": "手动快捷呼叫", "manualSiteRoam": "手动站点漫游", "mapping": "映射", "maxChannelLimit": "{zoneTitle}最多可配置%{count}个信道", "maydayRescue": "求救/救援设置", "mediumPowerFunc": "中功率", "memoryInfo": "内存信息", "messageTone": "短信提示音", "messageVibration": "短信振动", "micActiveTime": "麦克风激活时间(秒)", "middlePowerSosTime": "中电量求救发送间隔(min)", "minuteDiff": "分钟差数", "missedCall": "未接呼入", "modelDataError": "机型数据错误", "monitorSquelchMode": "监听静噪模式", "motionDetectionOnly": "仅运动检测", "multiKeyDecryption": "多密钥解密", "narrowBand": "窄带", "navType": "导航模式", "networking": "联网", "networkingMode": "联网模式", "newGroup": "新建群组", "newGroupContact": "新建组呼", "newSecretKey": "新建秘钥", "newSingleContact": "新建单呼", "noSubtone": "无亚音", "nonPriorityChannelDetection": "非优先信道检测", "nonstandardPhase": "非标准相位", "normal": "正常", "normallyOpen": "常开", "notHaveValidChannel": "设备无有效信道，请先配置信道数据", "notPoliteRetry": "不礼貌重试", "numberKeyFastDial": "数字键快捷拨号", "numericKeyboard": "数字键盘 {key}", "off": "关", "on": "开", "oneTouchCall": "单键呼叫", "oneTouchKey": "单键呼叫 {key}", "onlyChannel": "仅信道", "onlyEmergencyVoice": "仅紧急呼叫", "onlyGroupCall": "仅组呼", "onlySingleCall": "仅单呼", "onlyStill": "仅静止", "onlyTilt": "仅倾斜", "onlyVolume": "仅音量", "onlyWhistle": "仅鸣笛", "optionalFeatures": "选配功能", "optionalLockKey": "可选锁键", "orangeButton": "橙色按键", "ownGroup": "所属归属组", "passwordErrorNumber": "开机密码错误次数", "paste": "粘贴", "patrol": "巡更", "patrolClockIn": "巡更打卡", "patrolRecord": "巡更记录", "patrolSweepCard": "巡更扫卡", "phoneBook": "电话本", "phoneContact": "电话联系人", "phoneDialer": "电话拨号", "politeRetry": "礼貌重试", "powerAutoConversion": "功率自动转换", "powerLevel": "功率级别", "powerOnPwdRange": "开机密码输入范围：0-6", "powerSavingMode": "省电模式", "poweredTone": "开关机提示音", "presetChannel": "预设信道", "priorityChannelDetection": "优先信道检测", "priorityChannelTone": "优先信道提示音", "priorityInterrupt": "优先打断", "productionInfo": "生产信息", "promptTimeBackwards": "倒放预提示时间(秒)", "pttAlone": "是否PTT独立", "pttTimes": "PTT次数", "queryByCallDir": "按语音方向查询", "queryById": "按目标ID查询", "queryByTime": "按时间查询", "queryCommand": "查询命令", "quickSearch": "快捷查找", "randomKey": "随机密钥", "randomKeyEncryption": "随机密钥加密", "randomizedAlgorithm": "随机算法", "readPassword": "编程读密码", "realTime": "实时时间", "receiveDuration": "接收持续时间", "receiveLed": "接收指示灯", "receiveSquelchMode": "接收静噪模式", "receiverAlarmTone": "接收方报警提示音", "recordFile": "录音文件", "recordId": "记录ID", "recordList": "录音列表", "recordPlayback": "录音播放", "recordSetting": "录音设置", "recordSwitch": "录音开关", "recordTime": "时长", "recordingFunc": "录音功能", "recordingOption": "录音选配", "rejectStrangerCall": "拒绝陌生人呼叫", "remoteAlertCount": "远程提示次数", "remoteDestructionDecode": "远程销毁解码", "remoteDetectionDecode": "远程检测解码", "remoteEraseDecodeEnable": "远程销毁空口加密使能", "remoteKillActivateDecode": "遥毙/激活解码", "remoteKillActivateDecodeAuth": "遥毙/激活鉴权", "remoteMonitorAuth": "远程监听鉴权", "remoteMonitorDecode": "远程监听解码", "remoteMonitorDecodeEnable": "远程监听空口加密使能", "remoteMonitorDuration": "远程监听持续时间", "remotePromptDecode": "远程提示解码", "remoteShutEncryptEnable": "对讲机遥毙/唤醒空口加密使能", "remoteStunDecodeEnable": "远程遥晕/遥醒空口加密使能", "remoteStunWakeupDecode": "远程遥晕/遥醒解码", "reply": "回复", "replyChannel": "回复信道", "rescueScanHandUpTime": "救援扫描挂起时间(毫秒)", "resend": "重发", "residenceTime": "停留时间", "resourceVersion": "资源版本信息", "ringToneSettings": "铃音设置", "roamEnable": "漫游开/关", "roamList": "漫游列表", "roamLockSite": "漫游站点锁定", "roamManual": "手动站点漫游", "roamSettings": "漫游设置", "roaming": "漫游", "roamingGroup": "漫游组", "rssiDetectCycle": "RSSI检测周期(秒)", "runBackward": "倒放", "runBackwardAlarm": "倒放报警", "rxAnalogCdcss": "接收模拟亚音码", "rxCdcssType": "接收亚音类型", "rxDigitalCdcss": "接收数字亚音码", "rxGroupExceedLimit": "接收组数量上限为{max}，当前已超出{n}个", "sLocateFunc": "S(全功能)", "sameLaunchPermCondition": "同发射准许条件一致", "samePowerPassword": "同开机密码", "satellitePositionInfo": "卫星定位位置信息", "satellitePositionSwitch": "卫星定位开关", "saveModeOnTimeDelay": "省电模式延迟开启时间", "savePowerDelayTime": "省电延迟开启时间", "scan": "扫描", "scanAndRoamList": "扫描/漫游列表", "scanEdit": "扫描编辑", "scanGroupFormValidate": "扫描组数据验证失败", "scanList": "扫描列表", "scanOrRoamList": "扫描/漫游列表", "scanRoamStatusLed": "扫描/漫游状态指示灯", "scanSamplingTime": "扫描采样时间(毫秒)", "scanSettings": "扫描设置", "scanningEmissionMode": "扫描发射模式", "scanningGroup": "扫描组", "scramble": "扰频", "secondPriorityChannel": "第二优先信道", "secretKeyName": "密钥名称", "secretKeySwitch": "密钥开关", "secretKeyValue": "密钥值", "selectTargetModel": "请选择要创建的机型", "setDateTime": "设置时间和日期", "setRealTime": "设置实时时间", "shake": "振动", "shakeMode": "振动模式", "shiNeng": "使能", "shieldedHeadphones": "隐蔽模式屏蔽耳机", "shieldedKeyboardLock": "隐蔽模式屏蔽键盘锁", "shieldedLedLight": "隐蔽模式屏蔽指示灯", "shieldedMicrophone": "隐蔽模式屏蔽麦克风", "showContactContent": "显示联系人内容", "showStrangeNumber": "显示陌生号码", "signalingPassword": "信令密码", "signalingPwd": "空口密钥", "signalingSystem": "信令系统", "signalingType": "警报信令类型", "silenceAndVoice": "静默加语音", "silenceCarryVoice": "静默带语音", "simplex": "单工", "singleCallTone": "单呼提示音", "singleCallVibration": "单呼振动", "singleCallVoiceConfirm": "单呼语音确认", "singleKeyFuncCall": "单键功能呼叫", "siteInfo": "站点信息", "siteLock": "站点锁定", "siteSearchTimer": "站点搜索计时器(秒)", "sms": "短信", "smsUdpCompress": "短信UDP头压缩", "softKeyCallType": {"GROUP": "组呼", "MSG": "短信", "SINGLE": "单呼", "TIP": "呼叫提示"}, "softKeyFuncDefine": {"AGING_FUNC_TEST_ON_OFF": "老化功能测试开/关", "A_D_SWITCH": "数字模拟切换", "ActiveRFIDPRead": "有源RFID打卡", "AllMuteOnOff": "全部静音开/关", "BACKLIGNT": "背光灯自动开/关", "BACKLIGNT_SWITCH": "背光灯自动开/关", "BACKTOHOME": "返回主界面", "BASEBAND_FIRMWARE": "基带固件升级", "BATTERY_CHANGE": "电池电量提示", "BATTERY_CHARGE": "电池电量提示", "BLUETOOTH_SEARCH_AUTO_CONNECT": "蓝牙搜索并自动连接", "Back2BackForwardingMode": "背靠背转发模式", "BackToBackEnable": "背靠背开关", "BluetoothEnable": "蓝牙开关", "CALL_RECORDS": "呼叫记录", "CALL_TONE": "呼叫提示", "CH_DOWN": "信道下调", "CH_LOCK_SW": "信道锁定", "CH_PRESET1": "预设信道1", "CH_PRESET2": "预设信道2", "CH_PRESET3": "预设信道3", "CH_PRESET4": "预设信道4", "CH_UP": "信道上调", "COMMON_CONTACT_LIST": "常用联系人列表", "CONTACTS": "通讯录", "CONTAS_LIST_MENU": "联系人列表", "CONTAS_MENU": "通讯录", "DATE_TIME": "当前日期提示", "DEL_INVALID_CHL": "无用信道删除", "DEL_USELESS_CH": "无用信道删除", "DEV_ACTIVE": "对讲机激活", "DEV_DETECT": "对讲机检测", "DEV_DIE": "对讲机遥毙", "DEV_RUIN": "对讲机遥毁", "DEV_STUN": "对讲机遥晕", "DEV_WAKEUP": "对讲机遥醒", "DISCONNECT": "优先打断", "DTA": "数模转换", "DTMF_MENU": "DTMF键盘", "ENCRYPTION": "加密", "ERRNUMBER_RX": "误码测试-接收", "ERRNUMBER_TX": "误码测试-发射", "EncryptionSwitch": "加密开关", "GLARE_FLASHLIGHT": "强光手电开/关", "GPS_ON_OFF": "卫星定位开/关", "GPS_POSITION_SWITCH": "卫星定位开/关", "GPS_SWITCH": "卫星定位数据上传", "HANG_UP": "挂断", "HIDE_SWITCH": "隐蔽模式开/关", "INT_PRI": "优先打断", "KEYBOARD_LOCK": "键盘锁", "KEYLOCK": "键盘锁", "LONGMONI": "永久监听", "MAIN_MENU": "打开主菜单", "MANUAL_DIAL": "手动拨号", "MANUAL_ROAMING_LOCK": "手动漫游锁定", "MENU_INTERFACE": "菜单界面", "MONI": "监听", "MONITOR_ON_OFF": "监听", "MSG_MENU": "短信", "NETMODE_CHG": "中继/脱网", "NONE": "未设定", "NetworkingOnOff": "联网开/关", "OFF_NETWORK_ON_OFF": "脱网开/关", "PHONE_BOOK": "电话本", "PHONE_LIST_MENU": "电话联系人列表", "PRIORITY_INTERRUPT": "优先打断", "PWRMODE_CHG": "高/低功率", "PWRMODE_CHG_BP660": "功率切换", "PassiveRFIDRead": "无源RFID打卡", "RECORDING_ON_OFF": "录音开/关", "RECORD_SWITCH": "录音开/关", "RESCUE_SCAN_SWITCH": "救援扫描模式开/关", "RFIDQuery": "RFID积压查询", "RFID_REGISTER": "RFID登记", "RMT_MONITOR": "远程监听", "ROAM_MANUAL": "手动站点漫游", "ROAM_SWITCH": "漫游开/关", "SCALL_DIAL": "单呼手动拨号", "SCALL_DIAL_MENU": "手动拨号界面", "SCAN": "扫描开/关", "SCAN_SWITCH": "扫描开/关", "SITE_LOCK_SWITCH": "站点锁定开关", "SK_BT_SEARCH_AUTO_CONNECT": "蓝牙搜索并自动连接", "SK_BT_SWITCH": "蓝牙开/关", "SK_CH_LOCK": "信道锁定开/关", "SK_DISABLED_ALL_LED": "禁用所有LED", "SK_DNS_SWITCH": "降噪开/关", "SK_ENCRYPTION_SWITCH": "加密开/关", "SK_VIBRATION_SWITCH": "振动开/关", "SMS": "短信", "SPDCALL1": "单键功能呼叫1", "SPDCALL2": "单键功能呼叫2", "SPDCALL3": "单键功能呼叫3", "SPDCALL4": "单键功能呼叫4", "SPDCALL5": "单键功能呼叫5", "SPDCALL6": "单键功能呼叫6", "SQUELCH_LEVEL_ADJUST": "静噪级别调整", "SQUELCH_OPEN": "静噪打开", "STANDBY_INTERFACE": "待机界面", "STEALTH_MODE_ON_OFF": "隐蔽模式开/关", "SystemStatusQuery": "系统状态查询", "TELEMETRY_BUTTON1": "遥测按键1", "TELEMETRY_BUTTON2": "遥测按键2", "TELEMETRY_BUTTON3": "遥测按键3", "TONE_MUTE": "所有提示音开关", "TONE_MUTE_SWITCH": "所有提示音开关", "TOP_CONTAS_LIST_MENU": "常用联系人列表", "TRANSIENT_MONITOR": "暂态监听", "TRANSIENT_SQUELCH_OPEN": "暂态静噪打开", "TRANSMIT_POWER_HIGH_LOW": "发射功率高/低", "TX_TEST": "发射测试", "VIBRATION_SETTINGS": "振动设置", "VOLUME_UP": "音量上调", "VOLUMN_DOWN": "音量下调", "VOX": "声控开关", "VOX_SWITCH": "声控开/关", "WARNING_OFF": "紧急模式关闭", "WARNING_ON": "紧急模式开启", "WORK_ALONE_SWITCH": "单独工作开关", "WORK_DOWN_SWITCH": "倒放开关", "ZONE_DOWN": "区域下调", "ZONE_SWITCH": "区域切换", "ZONE_UP": "区域上调"}, "sosInfo": "求救信息", "sosRescue": "求救/救援", "sosRescueCfg": "求救/救援配置", "sosRescueChannel": "求救/救援信道", "specifiedChannel": "指定信道", "specifyTransmitChannel": "指定发射信道", "specifyTxTimeSlot": "指定发射时隙", "speechRate": "语速等级", "standardPhase": "标准相位", "stealthMode": "隐蔽模式", "stealthModeBacklight": "隐蔽模式背光灯", "stealthModeHeadsetMute": "隐蔽模式耳麦静音", "stealthModeSettings": "隐蔽模式设置", "stealthModeVibrationShield": "隐蔽模式屏蔽振动", "strengthen": "加强", "subtoneNotDetected": "不检测亚音", "subtoneScanningMode": "亚音扫描模式", "svtChannelList": "跨站漫游列表", "switchChannelSquelchMode": "切换信道静噪模式", "switchHighPowerThreshold": "切换到高功率门限(dBm)", "switchLowPowerThreshold": "切换到低功率门限(dBm)", "switchMediumPowerThreshold": "切换到中功率门限(dBm)", "syncTimezone": "同步时区", "synchronisedTime": "同步时间", "system": "系统", "systemFunction": "系统功能", "systemInfo": "系统信息", "targetId": "目标ID", "terminalType": "终端型号", "theAlarm": "警报", "theSelected": "选定的", "tiltOrMotionDetection": "倾斜或运动检测", "tiltOrStill": "倾斜或静止", "timeSlotSelection": "时隙选择", "timeZoneId": "时区ID", "transmitAlertAudioDuration": "发射警报音频持续时间(秒)", "transmitAlertAudioTimes": "发射警报音频次数", "triggerControl": "触发控制", "triggerInclination": "触发倾斜度(度)", "triggerMode": "触发方式", "ttsSpeed": "语速", "ttsTune": "语调", "ttsVolume": "音量", "txAnalogCdcss": "发射模拟亚音码", "txCdcssType": "发射亚音类型", "txDigitalCdcss": "发射数字亚音码", "uiSettings": "UI设置", "uiVersion": "UI版本", "unConfirmSingleCall": "非确认单呼短信", "underAlarmRemoteMonitorDecode": "紧急警报下远程监听解码", "unlimited": "无限", "unlimited2": "无限制", "upsideDown": "倒放", "upsideDownSwitch": "倒放开关", "urgentRemoteMonitorDecode": "紧急远程监听解码", "vibration": "振动", "vibrationSettings": "振动设置", "viewRecordingFiles": "查看录音文件", "virtualCluster": "虚拟集群", "virtualClusterFormValidate": "虚拟集群数据验证失败", "voiceBroadcast": "语音播报", "voiceBroadcastSettings": "语音播报设置", "voiceCallEmbedAlias": "语音呼叫嵌入别名", "voiceCallEmbedding": "语音呼叫嵌入位置信息", "voiceDuplex": "语音双工", "voiceEndTone": "语音结束音", "voiceLaunch": "语音发射", "voicePriority": "语音优先级", "voicePrompt": "声音提示", "weightingMark": "加重标志", "workAlone": "单独工作", "workAloneAlarm": "单独工作报警", "workAloneReminderTime": "单独工作提醒时间(秒)", "workResOptAlone": "单独工作响应操作", "workResTimeAlone": "单独工作响应时间(分)", "writePassword": "编程写密码", "xor": "异或", "zone": "区域", "zoneConfig": "新建区域列表", "zones": {"leaf": "三级区域", "parent": "二级区域", "root": "一级区域"}}, "dispatch": {"functionList": {"name": "多功能列表", "sendCommand": "发送命令", "keyboardDial": "键盘拨号", "audioSwitch": "音频开关", "broadcastCall": "广播全呼"}, "contactCard": {"sdcTerminal": "SDC对讲机", "networkTerminal": "网络对讲机", "group": "普通组", "taskGroup": "任务组", "fullCallContact": "全呼联系人", "locate": "定位", "call": "呼叫", "command": "命令", "message": "短信", "calling": "通话中"}}}