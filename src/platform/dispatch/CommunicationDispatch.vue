<template>
  <el-container class="flex-auto max-h-[100vh_-_146.5px] overflow-y-auto !px-[40px] !pb-[47px] !p-0 !flex flex-col gap-[15px] text-white">
    <el-aside class="flex flex-col gap-[15px]" :width="leftAsideWidth">
      <PageHeader :title="t('dispatch.functionList.name')" />
      <DispatchFunctionList />
    </el-aside>
    <el-main>
      <div class="text-white">Communication dispatch page</div>
    </el-main>
    <el-aside class="text-white" :width="rightAsideWidth">Aside</el-aside>
  </el-container>
</template>

<script setup lang="ts">
  import { useI18n } from 'vue-i18n'
  import { convertPxToRemWithUnit, calcScaleSize } from '@/utils/setRem'
  const { t } = useI18n()
  const leftAsideWidth = convertPxToRemWithUnit(calcScaleSize(282))
  const rightAsideWidth = convertPxToRemWithUnit(calcScaleSize(350))
</script>
