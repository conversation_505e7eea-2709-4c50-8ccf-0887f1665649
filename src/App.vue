<template>
  <el-config-provider :locale="elLocales">
    <router-view />
  </el-config-provider>
</template>

<script>
  import { needUpgrade } from '@/utils/checkbrowser'
  import { getLayoutLevel } from '@/utils/bfutil'
  import { nowLocalTime } from '@/utils/time'

  const isNeedUpgrade = needUpgrade()

  export default {
    name: 'App',
    data() {
      return {
        layoutLevel: 3,
      }
    },
    computed: {
      elLocales() {
        return this.$i18n.messages[this.$i18n.locale]
      },
    },
    beforeCreate() {
      // 替换当前路由为升级提示页面
      if (isNeedUpgrade) {
        this.$router.replace({ name: 'upgrade' })
      }
    },
    beforeMount() {
      bfglob.on('bflayout', level => {
        this.layoutLevel = level
      })
      bfglob.on('addnote', msg => {
        bfglob.cacheNotes.push(Object.freeze({ time: nowLocalTime(), content: msg }))
      })
      bfglob.emit('bflayout', getLayoutLevel())
    },
  }
</script>

<style lang="scss">
  /* 谷歌浏览器滚动条样式 */
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
    background-color: transparent;
  }

  ::-webkit-scrollbar-track {
    -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.2);
  }

  ::-webkit-scrollbar-track:hover {
    -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.4);
    background-color: rgba(0, 0, 0, 0.01);
  }

  ::-webkit-scrollbar-track:active {
    -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.4);
    background-color: rgba(0, 0, 0, 0.05);
  }

  ::-webkit-scrollbar-thumb {
    background-color: #0b3b7d;
    border-radius: 4px;
    -webkit-box-shadow: inset 1px 1px 0 rgba(0, 0, 0, 0.1);
  }

  #app {
    width: 100vw;
    height: 100vh;
  }

  body,
  ul,
  li,
  p,
  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    margin: 0;
    padding: 0;
  }

  body {
    font-family:
      Helvetica Neue,
      Helvetica,
      Arial,
      Microsoft YaHei,
      SimSun,
      sans-serif !important;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    font-size: 14px;
    color: #303133 !important;
    overflow: hidden;
  }

  ul,
  li {
    list-style-type: none;
  }

  a {
    text-decoration: none;
  }

  .lf {
    float: left;
  }

  .rt {
    float: right;
  }

  .hide {
    display: none !important;
  }

  .after:after {
    content: '';
    display: table;
    clear: both;
  }

  .left {
    text-align: left;
  }

  .center {
    text-align: center;
  }

  .right {
    text-align: right;
  }

  .flex {
    display: flex;
  }

  .flex-column {
    flex-direction: column;
  }

  .flex-item {
    flex: auto;
  }

  .justify-center {
    justify-content: center;
  }

  .justify-space-evenly {
    justify-content: space-evenly;
  }

  .align-center {
    align-items: center;
  }

  .word-break {
    white-space: normal;
    word-break: break-word;
  }

  .el-form {
    text-align: left;
    height: 100%;
    overflow: auto;

    .el-form-item {
      .el-form-item__label {
        @extend .word-break;
      }

      .el-form-item__content {
        & > .el-checkbox {
          @extend .word-break;
          display: flex;
          align-items: center;
          width: fit-content;

          .el-checkbox__input,
          .el-checkbox__label {
            @extend .word-break;
          }
        }
      }
    }
  }

  label {
    margin-bottom: 0 !important;
  }

  @media (max-width: 1199px) {
    .el-dialog--small {
      width: 60%;
    }
  }

  @media (max-width: 991px) {
    .el-dialog--small {
      width: 65%;
    }
  }

  @media (max-width: 767px) {
    .el-message-box {
      width: 100%;
    }
  }

  .loginLoading.el-loading-mask.loginLoading.is-fullscreen,
  .loginLoading .el-loading-spinner .el-loading-text {
    font-size: 3rem;
  }

  .el-select-dropdown {
    text-align: left;
  }

  body #app button:focus {
    outline: none;
  }

  .form-item-switch .el-form-item__content {
    padding-bottom: 1px;
  }

  body .el-select-dropdown__item {
    height: 26px;
    line-height: 26px;
  }

  .form-item-ellipsis .el-form-item__label > * {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .el-dialog.footer-border {
    .el-dialog__footer {
      border-top: 1px solid #ddd;
    }
  }

  // 解决element-ui select组件聚焦的时候会有闪现一下滚动条问题
  .el-input.el-input--small {
    .el-input__suffix {
      display: inline-block;
      width: 32px;
    }
  }
</style>
