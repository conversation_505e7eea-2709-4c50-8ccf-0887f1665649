<template>
  <el-main class="page-send-command">
    <section class="flex h-full send-command-container">
      <TableTree
        :ref="sendCmdTreeId"
        :treeId="sendCmdTreeId"
        :contextmenuOption="contextmenuOption"
        :option="treeOption"
        :filterOption="filterOption"
        class="send-command-tree"
        toolbar
        @select="selectNodes"
        @click="clickNode"
        @dblclick="dblclickNode"
        @loaded="treeLoaded"
      />

      <section class="flex-auto flex flex-col send-command-content p-4 pl-10">
        <el-radio-group v-model="cmdRadio" class="command-radio-group" @change="cmdRadioChange">
          <el-radio value="cb42">
            <span>{{ $t('writeFreq.satellitePositionSwitch') }}</span>
          </el-radio>
          <el-radio value="cb01">
            <span v-text="$t('dialog.locateCtrl')" />
          </el-radio>
          <el-radio value="cb02">
            <span v-text="$t('dialog.trailCtrl')" />
          </el-radio>
          <el-radio value="cb03">
            <span v-text="$t('dialog.areaSearch')" />
          </el-radio>
          <el-radio value="cb04">
            <span v-text="$t('dialog.electricFence')" />
          </el-radio>
          <el-radio value="cb05">
            <span v-text="$t('dialog.postFence')" />
          </el-radio>
          <el-radio value="cb06">
            <span v-text="$t('dialog.mobileCtrl')" />
          </el-radio>
          <el-radio value="cb07">
            <span v-text="$t('dialog.alarmSet')" />
          </el-radio>
          <el-radio value="cb10">
            <span v-text="$t('dialog.clearAlarm')" />
          </el-radio>
          <el-radio value="cb08">
            <span v-text="$t('dialog.voiceCtrl')" />
          </el-radio>
          <el-radio value="cb09">
            <span v-text="$t('dialog.telecontrol')" />
          </el-radio>
          <el-radio value="cb21">
            <span v-text="$t('dialog.switchCh')" />
          </el-radio>
          <el-radio value="cb31">
            <span v-text="$t('dialog.textMsg')" />
          </el-radio>
          <el-radio value="cb25" :disabled="!hasRfidAuth">
            <span v-if="hasRfidAuth" v-text="$t('dialog.gpsVirtualSet')" />
            <el-tooltip popper-class="bf-tooltip" v-else effect="dark" placement="top" :content="noRfidAuthToolTipText">
              <span v-text="$t('dialog.gpsVirtualSet')" />
            </el-tooltip>
          </el-radio>
        </el-radio-group>

        <section class="command-box mt-4 py-4">
          <!--卫星定位状态设置-->
          <el-form v-if="cmdRadio === 'cb42'" :model="cb42Cmd" :label-width="defaultLabelWidth" label-position="top" class="cb42">
            <el-form-item :label="$t('dialog.cmdOpts')">
              <el-select v-model="cb42Cmd.code">
                <el-option v-for="item in cb42CmdOptions" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
            <el-form-item class="action-form-item">
              <el-button type="primary" class="w-32" :disabled="sendCmdBtns.cb42" @click="send_cb42_cmd" v-text="$t('dialog.sendCmdTitle')" />
            </el-form-item>
          </el-form>
          <!--定位-->
          <el-form v-if="cmdRadio === 'cb01'" :model="cb01Cmd" :label-width="defaultLabelWidth" label-position="top" class="cb01">
            <el-form-item :label="$t('dialog.locateCount')">
              <el-input-number v-model="cb01Cmd.count" :min="1" :max="9999" />
            </el-form-item>
            <el-form-item :label="$t('dialog.locateSpacing')">
              <el-input-number v-model="cb01Cmd.spaceTime" :min="5" :max="9995" :step="5" />
            </el-form-item>
            <el-form-item :label="$t('dialog.size')">
              <el-input-number v-model="cb01Cmd.size" :min="0" :max="495" :step="5" />
            </el-form-item>
            <el-form-item class="action-form-item">
              <el-button type="primary" class="w-32" :disabled="sendCmdBtns.cb01" @click="send_cb01_cmd" v-text="$t('dialog.sendCmdTitle')" />
            </el-form-item>
          </el-form>
          <!--跟踪-->
          <el-form v-if="cmdRadio === 'cb02'" :model="cb02Cmd" :label-width="defaultLabelWidth" label-position="top" class="cb02">
            <el-form-item :label="$t('dialog.cmdOpts')">
              <el-select v-model="cb02Cmd.track" @change="cb02Cmd_changed">
                <el-option v-for="(item, index) in cb02Cmd_options" :key="index" :label="$t(item.label)" :value="item.value" />
              </el-select>
            </el-form-item>
            <el-form-item :label="$t('dialog.trailSpacing')">
              <el-input-number v-model="cb02Cmd.spaceTime" :min="5" :max="9995" :step="5" :disabled="disabled.cb02Cmd" />
            </el-form-item>
            <el-form-item :label="$t('dialog.size')">
              <el-input-number v-model="cb02Cmd.size" :min="0" :max="495" :step="5" :disabled="disabled.cb02Cmd" />
            </el-form-item>
            <el-form-item class="action-form-item">
              <el-button type="primary" class="w-32" :disabled="sendCmdBtns.cb02" @click="send_cb02_cmd" v-text="$t('dialog.sendCmdTitle')" />
            </el-form-item>
          </el-form>
          <!--区域查找-->
          <el-form v-if="cmdRadio === 'cb03'" :model="cb03Cmd" :label-width="defaultWidth" label-position="top" class="cb03">
            <div class="sendCmdHeightMax768">
              <el-form-item :label="$t('dialog.minLon')">
                <el-input v-model="cb03Cmd.minLon" />
              </el-form-item>
              <el-form-item :label="$t('dialog.minLat')">
                <el-input v-model="cb03Cmd.minLat" />
              </el-form-item>
              <el-form-item :label="$t('dialog.maxLon')">
                <el-input v-model="cb03Cmd.maxLon" />
              </el-form-item>
              <el-form-item :label="$t('dialog.maxLat')">
                <el-input v-model="cb03Cmd.maxLat" />
              </el-form-item>
            </div>
            <el-form-item class="action-form-item">
              <el-button type="primary" class="w-32" :class="[locale]" @click="get_lonLat_for_cb03" v-text="$t('dialog.getLngLat')" />
              <el-button type="primary" class="w-32" :disabled="sendCmdBtns.cb03" @click="send_cb03_cmd" v-text="$t('dialog.sendCmdTitle')" />
            </el-form-item>
          </el-form>
          <!--电子围栏-->
          <el-form v-if="cmdRadio === 'cb04'" :model="cb04Cmd" :label-width="isFR ? '160px' : defaultWidth" label-position="top" class="cbc04">
            <div class="sendCmdHeightMax768">
              <el-form-item :label="$t('dialog.cmdOpts')">
                <el-select v-model="cb04Cmd.setCmd" @change="cb04Cmd_changed">
                  <el-option v-for="(item, index) in cb04Cmd_options" :key="index" :label="$t(item.label)" :value="item.value" />
                </el-select>
              </el-form-item>
              <el-form-item :label="$t('dialog.crossTOT')">
                <el-input-number v-model="cb04Cmd.spaceTime" :min="1" :max="99" :disabled="disabled.cb04Cmd" />
              </el-form-item>
              <el-form-item :label="$t('dialog.minLon')">
                <el-input v-model="cb04Cmd.minLon" :disabled="disabled.cb04Cmd" />
              </el-form-item>
              <el-form-item :label="$t('dialog.minLat')">
                <el-input v-model="cb04Cmd.minLat" :disabled="disabled.cb04Cmd" />
              </el-form-item>
              <el-form-item :label="$t('dialog.maxLon')">
                <el-input v-model="cb04Cmd.maxLon" :disabled="disabled.cb04Cmd" />
              </el-form-item>
              <el-form-item :label="$t('dialog.maxLat')">
                <el-input v-model="cb04Cmd.maxLat" :disabled="disabled.cb04Cmd" />
              </el-form-item>
            </div>
            <el-form-item class="action-form-item">
              <el-button
                type="primary"
                class="w-32"
                :class="[locale]"
                :disabled="disabled.cb04Cmd"
                @click="get_lonLat_for_cb04"
                v-text="$t('dialog.getLngLat')"
              />
              <el-button type="primary" class="w-32" :disabled="sendCmdBtns.cb04" @click="send_cb04_cmd" v-text="$t('dialog.sendCmdTitle')" />
            </el-form-item>
          </el-form>
          <!--岗哨围栏-->
          <el-form v-if="cmdRadio === 'cb05'" :model="cb05Cmd" :label-width="spaceTimeLabelWidth" label-position="top" class="cb05">
            <div class="sendCmdHeightMax768">
              <el-form-item :label="$t('dialog.cmdOpts')">
                <el-select v-model="cb05Cmd.setCmd" @change="cb05Cmd_changed">
                  <el-option v-for="(item, index) in cb05Cmd_options" :key="index" :label="$t(item.label)" :value="item.value" />
                </el-select>
              </el-form-item>
              <el-form-item :label="$t('dialog.leaveTOT')">
                <el-input-number v-model="cb05Cmd.spaceTime" :min="1" :max="99" :disabled="disabled.cb05Cmd" />
              </el-form-item>
              <el-form-item :label="$t('dialog.sentinelRadius')">
                <el-input-number v-model="cb05Cmd.radius" :min="10" :max="100" :disabled="disabled.cb05Cmd" />
              </el-form-item>
              <el-form-item :label="$t('dialog.lon')">
                <el-input v-model="cb05Cmd.lon" :disabled="disabled.cb05Cmd" />
              </el-form-item>
              <el-form-item :label="$t('dialog.lat')">
                <el-input v-model="cb05Cmd.lat" :disabled="disabled.cb05Cmd" />
              </el-form-item>
            </div>
            <el-form-item class="action-form-item">
              <el-button
                type="primary"
                class="w-32"
                :class="[locale]"
                :disabled="disabled.cb05Cmd"
                @click="get_lonLat_for_cb05"
                v-text="$t('dialog.getLngLat')"
              />
              <el-button type="primary" class="w-32" :disabled="sendCmdBtns.cb05" @click="send_cb05_cmd" v-text="$t('dialog.sendCmdTitle')" />
            </el-form-item>
          </el-form>
          <!--移动监控-->
          <el-form v-if="cmdRadio === 'cb06'" :model="cb06Cmd" :label-width="spaceTimeLabelWidth" label-position="top" class="cb06">
            <div class="sendCmdHeightMax768">
              <el-form-item :label="$t('dialog.cmdOpts')">
                <el-select v-model="cb06Cmd.setCmd" @change="cb06Cmd_changed">
                  <el-option v-for="(item, index) in cb06Cmd_options" :key="index" :label="$t(item.label)" :value="item.value" />
                </el-select>
              </el-form-item>
              <el-form-item :label="$t('dialog.stopTOT')">
                <el-input-number v-model="cb06Cmd.spaceTime" :min="1" :max="99" :disabled="disabled.cb06Cmd" />
              </el-form-item>
              <el-form-item :label="$t('dialog.lonDif')">
                <el-input-number v-model="cb06Cmd.lonDif" :min="1" :max="99" :disabled="disabled.cb06Cmd" />
              </el-form-item>
              <el-form-item :label="$t('dialog.latDif')">
                <el-input-number v-model="cb06Cmd.latDif" :min="1" :max="99" :disabled="disabled.cb06Cmd" />
              </el-form-item>
            </div>
            <el-form-item class="action-form-item">
              <el-button type="primary" class="w-32" :disabled="sendCmdBtns.cb06" @click="send_cb06_cmd" v-text="$t('dialog.sendCmdTitle')" />
            </el-form-item>
          </el-form>
          <!--报警设置-->
          <el-form v-if="cmdRadio === 'cb07'" :model="cb07Cmd" :label-width="defaultLabelWidth" label-position="top" class="cb07">
            <el-form-item :label="$t('dialog.cmdOpts')">
              <el-select v-model="cb07Cmd.setCmd" @change="cb07Cmd_changed">
                <el-option v-for="(item, index) in cb07Cmd_options" :key="index" :label="$t(item.label)" :value="item.value" />
              </el-select>
            </el-form-item>
            <el-form-item :label="$t('dialog.listenTime')">
              <el-input-number v-model="cb07Cmd.jtTime" :min="0" :max="99" :disabled="disabled.cb07Cmd" />
            </el-form-item>
            <el-form-item :label="$t('dialog.locateSpacing')">
              <el-input-number v-model="cb07Cmd.dwTime" :min="0" :max="99" :disabled="disabled.cb07Cmd" />
            </el-form-item>
            <el-form-item class="action-form-item">
              <el-button type="primary" class="w-32" :disabled="sendCmdBtns.cb07" @click="send_cb07_cmd" v-text="$t('dialog.sendCmdTitle')" />
            </el-form-item>
          </el-form>
          <!--语音监控-->
          <el-form v-if="cmdRadio === 'cb08'" :model="cb08Cmd" :label-width="defaultLabelWidth" label-position="top" class="cb08">
            <el-form-item :label="$t('dialog.cmdOpts')">
              <el-select v-model="cb08Cmd.setCmd" @change="cb08Cmd_changed">
                <el-option v-for="(item, index) in cb08Cmd_options" :key="index" :label="$t(item.label)" :value="item.value" />
              </el-select>
            </el-form-item>
            <el-form-item :label="$t('dialog.listenTime')">
              <el-input-number v-model="cb08Cmd.jtTime" :min="1" :max="99" :disabled="disabled.cb08Cmd" />
            </el-form-item>
            <el-form-item class="action-form-item">
              <el-button type="primary" class="w-32" :disabled="sendCmdBtns.cb08" @click="send_cb08_cmd" v-text="$t('dialog.sendCmdTitle')" />
            </el-form-item>
          </el-form>
          <!--遥开遥毙-->
          <el-form v-if="cmdRadio === 'cb09'" :model="cb09Cmd" :label-width="defaultWidth" label-position="top" class="cb09">
            <el-form-item :label="$t('dialog.cmdOpts')">
              <el-select v-model="cb09Cmd.setCmd" @change="cb09Cmd_changed">
                <el-option v-for="(item, index) in cb09Cmd_options" :key="index" :label="$t(item.label)" :value="item.value" />
              </el-select>
            </el-form-item>
            <el-form-item :label="$t('dialog.lockedStatus')">
              <el-select v-model="cb09Cmd.status" :disabled="disabled.cb09Cmd">
                <el-option v-for="(item, index) in cb09Cmd_stats_options" :key="index" :label="$t(item.label)" :value="item.value" :disabled="item.disabled" />
              </el-select>
            </el-form-item>
            <el-form-item class="action-form-item">
              <el-button type="primary" class="w-32" :disabled="sendCmdBtns.cb09" @click="send_cb09_cmd" v-text="$t('dialog.sendCmdTitle')" />
            </el-form-item>
          </el-form>
          <!--解除报警-->
          <el-form v-if="cmdRadio === 'cb10'" label-position="top" class="cb10">
            <el-form-item class="action-form-item">
              <el-button type="primary" class="w-32" :disabled="sendCmdBtns.cb10" @click="send_cb10_cmd" v-text="$t('dialog.sendCmdTitle')" />
            </el-form-item>
          </el-form>
          <!--切换信道-->
          <el-form v-if="cmdRadio === 'cb21'" :model="cb21Cmd" :label-width="defaultLabelWidth" label-position="top" class="cb21">
            <el-form-item :label="$t('dialog.switchCh')">
              <el-input-number v-model="cb21Cmd.channel" :min="1" :max="4095" />
            </el-form-item>
            <el-form-item class="action-form-item">
              <el-button type="primary" class="w-32" :disabled="sendCmdBtns.cb21" @click="send_cb21_cmd" v-text="$t('dialog.sendCmdTitle')" />
            </el-form-item>
          </el-form>
          <!--文本短信-->
          <el-form v-if="cmdRadio === 'cb31'" :model="bc31Cmd" :label-width="defaultWidth" label-position="top" class="cb31">
            <el-form-item :label="$t('dialog.smsType')">
              <el-select v-model="bc31Cmd.smsType">
                <el-option v-for="(item, index) in smsTypeList" :key="index" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
            <el-form-item :label="$t('dialog.smsContent')">
              <el-input v-model="bc31Cmd.message" :maxlength="smsLenLimit" :autosize="{ minRows: 3, maxRows: 5 }" type="textarea" resize="none" />
              <div
                class="text-gray-400 text-xs text-right sms-input-tips"
                :class="{
                  'text-red-500': bc31Cmd.message.length >= smsLenLimit,
                }"
              >
                {{ bc31Cmd.message.length }} / {{ smsLenLimit }}
              </div>
            </el-form-item>
            <el-form-item class="action-form-item">
              <el-button type="primary" class="w-32" :disabled="sendCmdBtns.bc31" @click="send_bc31_cmd" v-text="$t('dialog.sendCmdTitle')" />
            </el-form-item>
          </el-form>
          <!--GPS巡查点设置-->
          <el-form v-if="cmdRadio === 'cb25'" :model="cb25Cmd" :label-width="defaultLabelWidth" label-position="top" class="cb25">
            <div class="sendCmdHeightMax768">
              <el-form-item :label="$t('dialog.cmdOpts')">
                <el-select v-model="cb25Cmd.setCmd" @change="cb25Cmd_changed">
                  <el-option v-for="(item, index) in cb25Cmd_options" :key="index" :label="$t(item.label)" :value="item.value" />
                </el-select>
              </el-form-item>
              <el-form-item :label="$t('dialog.Gsource')">
                <el-select
                  v-model="cb25Cmd.gpsPoint"
                  filterable
                  clearable
                  :placeholder="$t('dialog.select')"
                  :disabled="disabled.cb25Cmd"
                  :no-match-text="$t('dialog.noMatchText')"
                  @change="cb25Cmd_gpsPoint_changed"
                >
                  <el-option v-for="(item, index) in gpsPoints" :key="index" :label="item.label" :value="item.value" />
                </el-select>
              </el-form-item>
              <el-form-item :label="$t('dialog.gpsCardNo')">
                <el-input v-model="cb25Cmd.pointCard" :disabled="true" />
              </el-form-item>
              <el-form-item :label="$t('dialog.gpsRadius')">
                <el-input v-model="cb25Cmd.radius" :disabled="true" />
              </el-form-item>
              <el-form-item :label="$t('dialog.savePenNo')" :label-width="cb25PointLabelWidth" prop="message">
                <el-input-number v-model="cb25Cmd.pointN" :min="1" :max="20" :disabled="disabled.cb25Cmd_No" />
              </el-form-item>
            </div>
            <el-form-item class="action-form-item">
              <el-button type="primary" class="w-32" :disabled="sendCmdBtns.cb25" @click="send_cb25_cmd" v-text="$t('dialog.sendCmdTitle')" />
            </el-form-item>
          </el-form>
        </section>
      </section>
    </section>

    <!-- 获取经纬度和范围的地图 -->
    <template v-if="isFirstLoadBaseMap">
      <base-map ref="baseMap" v-model:visible="mapVisible" class="send-command-map-container" :controls="mapControls" @init="onInitMap" @close="onCloseMap">
        <template #topCenter>
          <div v-if="getOneLonLat" class="get-coordinate-tips">
            {{ $t('map.clickMapGetCoordinates') }}
          </div>
          <div v-else class="get-range-coordinates-tips" v-html="$t('map.selectCoordinatesTips', { iconEl: lonLatControlIconEl })" />
        </template>
      </base-map>
    </template>
  </el-main>
</template>

<script>
  import TableTree from '@/components/common/tableTree'
  import bfprocess from '@/utils/bfprocess'
  import bftree from '@/utils/bftree'
  import bfutil, { checkTerminalIsSupportCommand, deferred, lonLatValidate, NotSupportCommandDeviceTypes } from '@/utils/bfutil'
  import { SelectLngLatControl } from '@/utils/map'
  import bfNotify from '@/utils/notify'
  import qWebChannel from '@/utils/qWebChannelObj'
  import { checkLicenseAuthorized, checkLicenseWithModuleName, getAuthModuleI18nKey, getLicense, LicenseModuleNames } from '@/utils/bfAuth'
  import vueMixin from '@/utils/vueMixin'
  import BaseMap from '@/components/common/BaseMap.vue'
  import eventBus from '@/utils/eventBus'
  import { useRouteParams } from '@/router'

  let lngLatMap
  const lngLatMapReady = deferred()

  // 不支持组呼命令的cbxx指令
  const NotSupportGroupCallOperationCommands = ['cb42']
  const { getRouteParams } = useRouteParams()

  export default {
    name: 'BfSendcmd',
    mixins: [vueMixin],
    data() {
      return {
        isFirstLoadBaseMap: false,
        mapVisible: false,
        getOneLonLat: true,
        selectLngLatControl: new SelectLngLatControl(),
        qWebChannel,
        qWebServer: null,
        isInitTree: false,
        sendCmdTreeId: 'sendCmdTree',
        cmdTarget: {
          groud: [],
          device: [],
        },
        cmdRadio: 'cb01',
        gpsPoints: [],
        cb42Cmd: {
          // code [uint8]: 10:关闭 11:开启 12:查询
          code: 12,
        },
        cb01Cmd: {
          count: 1,
          spaceTime: 5,
          size: 20,
        },
        cb02Cmd: {
          track: 1,
          spaceTime: 5,
          size: 20,
        },
        cb02Cmd_options: [
          {
            value: 0,
            label: 'dialog.cancel',
          },
          {
            value: 1,
            label: 'dialog.starting',
          },
          {
            value: 2,
            label: 'nav.enquiry',
          },
        ],
        cb03Cmd: {
          minLon: '',
          minLat: '',
          maxLon: '',
          maxLat: '',
        },
        cb04Cmd: {
          setCmd: 2,
          penN: 1,
          spaceTime: 1,
          minLon: '',
          minLat: '',
          maxLon: '',
          maxLat: '',
        },
        cb04Cmd_options: [
          {
            value: 0,
            label: 'dialog.removeFenceAllCtrl',
          },
          {
            value: 1,
            label: 'dialog.enableInCacnelOut',
          },
          {
            value: 2,
            label: 'dialog.enableOutCacnelIn',
          },
          // {value: 3, label: "dialog.enableInAndOut"},
          {
            value: 9,
            label: 'nav.enquiry',
          },
        ],
        cb05Cmd: {
          setCmd: 1,
          spaceTime: 1,
          lon: '',
          lonDif: '66',
          lat: '',
          latDif: '57',
          radius: 50,
        },
        cb05Cmd_options: [
          {
            value: 0,
            label: 'dialog.cancel',
          },
          {
            value: 1,
            label: 'dialog.starting',
          },
          {
            value: 2,
            label: 'nav.enquiry',
          },
        ],
        cb06Cmd: {
          setCmd: 1,
          spaceTime: 1,
          lonDif: 65,
          latDif: 75,
        },
        cb06Cmd_options: [
          {
            value: 0,
            label: 'dialog.cancel',
          },
          {
            value: 1,
            label: 'dialog.starting',
          },
          {
            value: 2,
            label: 'nav.enquiry',
          },
        ],
        cb07Cmd: {
          setCmd: 1,
          jtTime: 20,
          dwTime: 10,
        },
        cb07Cmd_options: [
          {
            value: 0,
            label: 'dialog.cancel',
          },
          {
            value: 1,
            label: 'dialog.starting',
          },
          {
            value: 2,
            label: 'nav.enquiry',
          },
        ],
        cb08Cmd: {
          setCmd: 1,
          jtCh: 0,
          jtTime: 30,
        },
        voipServerConnected: false,
        cb08Cmd_options: [
          {
            value: 0,
            label: 'dialog.cancelListen',
          },
          {
            value: 1,
            label: 'dialog.enableListen',
          },
        ],
        cb09Cmd: {
          setCmd: 1,
          status: 2,
        },
        cb09Cmd_options: [
          {
            value: 0,
            label: 'dataTable.powerOn',
          },
          {
            value: 1,
            label: 'dialog.lockedDev',
          },
          {
            value: 2,
            label: 'nav.enquiry',
          },
        ],
        cb09Cmd_stats_options: [
          {
            value: 0,
            label: 'dataTable.powerOn',
            disabled: false,
          },
          {
            value: 1,
            label: 'dialog.disListen',
            disabled: false,
          },
          {
            value: 2,
            label: 'dialog.disSend',
            disabled: false,
          },
          {
            value: 3,
            label: 'dialog.disSL',
            disabled: false,
          },
        ],
        cb21Cmd: {
          setCmd: 1,
          channel: 1,
        },
        cb21Cmd_options: [
          {
            value: 0,
            label: 'dialog.cancelCenterCHCtrl',
          },
          {
            value: 1,
            label: 'dialog.openCenterCHCtrl',
          },
        ],
        bc31Cmd: {
          setCmd: 1,
          message: '',
          sendTime: '',
          codeTP: 2,
          smsType: '02',
        },
        cb25Cmd: {
          setCmd: 1,
          pointN: 1,
          pointCard: '',
          gpsPoint: '',
          radius: '',
          lon: '',
          lat: '',
          lonDif: '20',
          latDif: '20',
        },
        cb25Cmd_options: [
          {
            value: 0,
            label: 'dialog.cancel',
          },
          {
            value: 1,
            label: 'dialog.starting',
          },
          {
            value: 2,
            label: 'nav.enquiry',
          },
        ],
        sendCmdBtns: {
          cb01: false,
          cb02: false,
          cb03: false,
          cb04: false,
          cb05: false,
          cb06: false,
          cb07: false,
          cb08: false,
          cb09: false,
          cb10: false,
          cb11: false,
          cb21: false,
          cb24: false,
          cb25: false,
          bc31: false,
          cb42: false,
        },
        disabled: {
          cb02Cmd: false,
          cb04Cmd: false,
          cb05Cmd: false,
          cb06Cmd: false,
          cb07Cmd: false,
          cb08Cmd: false,
          cb09Cmd: false,
          cb25Cmd: false,
          cb25Cmd_No: false,
        },
        smsLenLimit: 140,
      }
    },
    methods: {
      onInitMap(map) {
        lngLatMap = map
        lngLatMapReady.resolve(true)
      },
      onCloseMap() {
        this.mapVisible = false
        eventBus.emit('map-close')
      },
      treeLoaded() {
        this.isInitTree = true
        this.loadCommandTree()
      },
      selectNodes(event, data) {
        const selectKeys = $.map(data.tree.getSelectedNodes(), function (node) {
          return node.key
        })
        const selRootNodes = data.tree.getSelectedNodes(true)
        this.cmdTarget = {
          groud: [],
          device: [],
        }
        this.filterSelectNodes(selectKeys, selRootNodes)
      },
      clickNode(event, data) {
        const excludeList = ['expander', 'prefix', 'checkbox']
        if (excludeList.includes(data.targetType)) {
          return true
        }
        const node = data.node
        if (!node) {
          return false
        }
        node.setActive()
        node.setSelected(!node.isSelected())
      },
      dblclickNode(event, data) {
        if (!data.node) {
          return
        }
        data.node.setSelected(true)
      },
      openDlgFn() {
        // if (this.isInitTree) {
        //   this.loadCommandTree()
        //   this.tableTree?.reFilterOnline()
        // }
        this.qWebServer = this.qWebChannel.server
        if (bfglob.vspeaking) {
          this.voipServerConnected = bfglob.vspeaking.voipServerConnected
        }
      },

      selectAll(bool) {
        bftree.selectAll(this.sendCmdTreeId, bool)
      },
      dictNodeIsSelected(dict) {
        if (!dict || !dict.data) {
          return false
        }

        if (this.cmdTarget.device.includes(dict.data.dmrId)) {
          return true
        }

        return this.cmdTarget.groud.includes(dict.data.dmrId)
      },
      loadCommandTree() {
        this.tableTree.toDictTree('bftree', dict => {
          // 过滤中继虚拟手台数据，不支持发送命令
          if (dict.data && !dict.data.isOrg && NotSupportCommandDeviceTypes.includes(dict.data.deviceType)) {
            return false
          }
          // 判断之前是否有选中节点，有则设置选中状态
          dict.partsel = false
          dict.selected = this.dictNodeIsSelected(dict)
          dict.expanded = true
        })
        this.disableNotSupportCommandNodes(this.cmdRadio)
      },
      // 筛选选中的命令目标
      filterSelectNodes(selKeys, selRootNodes) {
        const addTarget = (target, type = 2) => {
          // type==1为单位，2为对讲机
          if (!target) {
            return
          }

          const targetName = type === 1 ? 'groud' : 'device'
          !this.cmdTarget[targetName].includes(target) && this.cmdTarget[targetName].push(target)
        }
        for (let i = 0; i < selKeys.length; i++) {
          let key = selKeys[i]
          key = key.length > 36 ? key.slice(0, 36) : key
          const device = bfglob.gdevices.get(key)
          if (device) {
            // 目标是设备
            addTarget(device.dmrId, 2)
          } else {
            // 目标是组织
            const orgItem = bfglob.gorgData.get(key)
            if (orgItem) {
              addTarget(orgItem.dmrId, 1)
            }
          }
        }
      },
      update_tree_node(device) {
        bftree.updateDeviceNodeTitle(this.sendCmdTreeId, device)
      },

      get_gpsPoint_data_for_setGpsPoint() {
        const gpsPoints = []
        const g_linePoint = bfglob.glinePoints.getAll()
        for (const key in g_linePoint) {
          const item = g_linePoint[key]
          if (item.pointType !== 3) continue

          const gpsPoint = {
            value: item.pointRfid,
            label: item.pointId + '/' + item.pointName,
          }
          gpsPoints.push(gpsPoint)
        }

        this.gpsPoints = gpsPoints
      },
      // 禁用树节点不支持当前命令的选择功能
      disableNotSupportCommandNodes(cmdRadio = this.cmdRadio) {
        const newDeviceTarget = new Set()
        const notSupportGroupCallOperation = NotSupportGroupCallOperationCommands.includes(cmdRadio)
        // 遍历所有树节点，判断其中的终端节点是否支持cmdRadio命令
        const forEachNodes = nodes => {
          for (let i = 0; i < nodes.length; i++) {
            const node = nodes[i]
            // 单位节点，递归处理子节点
            if (node.data.isOrg) {
              if (notSupportGroupCallOperation) {
                node.selected = false
              }
              node.unselectable = notSupportGroupCallOperation
              node.unselectableStatus = notSupportGroupCallOperation
              node.render()
              forEachNodes(node.getChildren() ?? [])
              continue
            }

            // 终端节点，判断是否支持
            const isSupported = checkTerminalIsSupportCommand(node.data.dmrId, cmdRadio)
            !isSupported && node.setSelected(false) // 不支持才取消选中状态
            node.unselectable = isSupported ? undefined : true // undefined为取消禁用，其他为禁用
            node.unselectableStatus = isSupported ? undefined : true // undefined为取消禁用，其他为禁用
            node.render()

            // 缓存支持命令的终端的dmrId，以便过滤当前已选中的终端目标
            if (isSupported) {
              newDeviceTarget.add(node.data.dmrId)
            }
          }
        }
        const nodes = this.tableTree?.getRootNode()?.getChildren() ?? []
        forEachNodes(nodes)

        // 重置当前选中的终端目标
        this.cmdTarget.device = this.cmdTarget.device.filter(item => newDeviceTarget.has(item))
        // 不支持组呼命令
        if (notSupportGroupCallOperation) {
          this.cmdTarget.groud = []
        }
      },
      cmdRadioChange(label) {
        if (label === 'cb25') {
          this.get_gpsPoint_data_for_setGpsPoint()
        }

        this.disableNotSupportCommandNodes(this.cmdRadio)
      },
      cb02Cmd_changed(val) {
        if (val === 1) {
          this.disabled.cb02Cmd = false
        } else {
          this.disabled.cb02Cmd = true
        }
      },
      cb04Cmd_changed(val) {
        if (val === 0 || val === 9) {
          this.disabled.cb04Cmd = true
        } else {
          this.disabled.cb04Cmd = false
        }
      },
      cb05Cmd_changed(val) {
        if (val === 1) {
          this.disabled.cb05Cmd = false
        } else {
          this.disabled.cb05Cmd = true
        }
      },
      cb06Cmd_changed(val) {
        if (val === 1) {
          this.disabled.cb06Cmd = false
        } else {
          this.disabled.cb06Cmd = true
        }
      },
      cb07Cmd_changed(val) {
        if (val === 1) {
          this.disabled.cb07Cmd = false
        } else {
          this.disabled.cb07Cmd = true
        }
      },
      cb08Cmd_changed(val) {
        if (val === 1) {
          this.disabled.cb08Cmd = false
        } else {
          this.disabled.cb08Cmd = true
        }
      },
      cb09Cmd_changed(val) {
        this.disabled.cb09Cmd = false
        if (val === 0) {
          this.cb09Cmd.status = 0
          this.cb09Cmd_stats_options[0].disabled = false
          this.cb09Cmd_stats_options[1].disabled = true
          this.cb09Cmd_stats_options[2].disabled = true
          this.cb09Cmd_stats_options[3].disabled = true
        } else if (val === 1) {
          this.cb09Cmd.status = 2
          this.cb09Cmd_stats_options[0].disabled = true
          this.cb09Cmd_stats_options[1].disabled = false
          this.cb09Cmd_stats_options[2].disabled = false
          this.cb09Cmd_stats_options[3].disabled = false
        } else if (val === 2) {
          this.cb09Cmd.status = 2
          this.disabled.cb09Cmd = true
        }
      },
      cb25Cmd_changed(val) {
        if (val === 2) {
          this.disabled.cb25Cmd = true
        } else {
          this.disabled.cb25Cmd = false
        }
        if (val === 0) {
          this.disabled.cb25Cmd_No = true
        } else {
          this.disabled.cb25Cmd_No = false
        }
      },
      cb25Cmd_gpsPoint_changed(pointRfid) {
        if (!pointRfid) {
          this.cb25Cmd.pointCard = ''
          this.cb25Cmd.radius = ''
          this.cb25Cmd.lon = ''
          this.cb25Cmd.lat = ''
        }
        var gpsPoint = bfglob.glinePoints.getDataByIndex(pointRfid)
        if (gpsPoint) {
          this.cb25Cmd.pointCard = gpsPoint.pointRfid
          this.cb25Cmd.radius = gpsPoint.gpsPointRadius
          this.cb25Cmd.lon = gpsPoint.lon
          this.cb25Cmd.lat = gpsPoint.lat
        }
      },
      // 跳转首页选择坐标范围
      get_lonLat_func(lonLatObj) {
        this.isFirstLoadBaseMap = true
        this.mapVisible = true
        this.getOneLonLat = false

        this.selectLngLatControl.enable(true)
        // 重写控件的select事件
        this.selectLngLatControl.select = data => {
          lonLatObj.minLon = data.minLon
          lonLatObj.minLat = data.minLat
          lonLatObj.maxLon = data.maxLon
          lonLatObj.maxLat = data.maxLat

          this.$refs.baseMap.close()
        }
      },
      get_lonLat_for_cb03() {
        this.get_lonLat_func(this.cb03Cmd)
      },
      get_lonLat_for_cb04() {
        this.get_lonLat_func(this.cb04Cmd)
      },
      get_lonLat_for_cb05() {
        this.isFirstLoadBaseMap = true
        this.mapVisible = true
        this.getOneLonLat = true
        this.selectLngLatControl.enable(false)

        const finish = () => {
          this.mapVisible = false
          lngLatMap.getCanvas().style.cursor = ''
        }
        const setLngLat = lngLat => {
          this.cb05Cmd.lon = lngLat.lng
          this.cb05Cmd.lat = lngLat.lat
        }
        const mapOnClick = evt => {
          setLngLat(evt.lngLat)
          onMapClose()
        }

        const onMapClose = () => {
          lngLatMap.off('click', mapOnClick)
          finish()
        }

        lngLatMapReady.then(() => {
          eventBus.once('map-close', onMapClose)
          lngLatMap.getCanvas().style.cursor = 'crosshair'
          lngLatMap.on('click', mapOnClick)
        })
      },

      // 发送命令后禁用按钮，默认3秒
      disabledSendCmdFunc(btn, sec = 3000) {
        this.sendCmdBtns[btn] = true
        setTimeout(
          function () {
            this.sendCmdBtns[btn] = false
          }.bind(this),
          sec
        )
      },
      send_cb42_cmd() {
        if (this.cmdTarget.groud.length === 0 && this.cmdTarget.device.length === 0) {
          bfNotify.warningBox(this.$t('msgbox.selectTarget'), 'error')
          return
        }
        bfprocess.cb42(this.cmdTarget, this.cb42Cmd)
        this.disabledSendCmdFunc('cb42')
      },
      send_cb01_cmd() {
        if (this.cmdTarget.groud.length === 0 && this.cmdTarget.device.length === 0) {
          bfNotify.warningBox(this.$t('msgbox.selectTarget'), 'error')
          return
        }
        const _size = (this.cb01Cmd.size / 5).toFixed(0) * 5
        const _spaceTime = (this.cb01Cmd.spaceTime / 5).toFixed(0) * 5
        this.cb01Cmd.size = parseInt(_size)
        this.cb01Cmd.spaceTime = parseInt(_spaceTime)
        bfprocess.cb01(this.cmdTarget, this.cb01Cmd)
        this.disabledSendCmdFunc('cb01')
      },
      send_cb02_cmd() {
        if (this.cmdTarget.groud.length === 0 && this.cmdTarget.device.length === 0) {
          bfNotify.warningBox(this.$t('msgbox.selectTarget'), 'error')
          return
        }
        const _size = (this.cb02Cmd.size / 5).toFixed(0) * 5
        const _spaceTime = (this.cb02Cmd.spaceTime / 5).toFixed(0) * 5
        this.cb02Cmd.size = parseInt(_size)
        this.cb02Cmd.spaceTime = parseInt(_spaceTime)
        bfprocess.cb02(this.cmdTarget, this.cb02Cmd)
        this.disabledSendCmdFunc('cb02')
      },
      send_cb03_cmd() {
        if (this.cb03Cmd.minLon === '' || this.cb03Cmd.minLat === '' || this.cb03Cmd.maxLon === '' || this.cb03Cmd.maxLat === '') {
          bfNotify.warningBox(this.$t('msgbox.selectLngLat'), 'error')
          return
        }

        // 可能输入非数字字符串
        if (isNaN(this.cb03Cmd.minLon) || isNaN(this.cb03Cmd.minLat) || isNaN(this.cb03Cmd.maxLon) || isNaN(this.cb03Cmd.maxLat)) {
          bfNotify.warningBox(this.$t('msgbox.invalidLngLat'), 'error')
          return
        }

        // 经纬度可能是用户输入数字字符串，需要转换为数字
        ;['minLon', 'minLat', 'maxLon', 'maxLat'].forEach(key => {
          this.cb03Cmd[key] = +this.cb03Cmd[key]
        })

        if (!lonLatValidate([this.cb03Cmd.minLon, this.cb03Cmd.minLat]) || !lonLatValidate([this.cb03Cmd.maxLon, this.cb03Cmd.maxLat])) {
          bfNotify.warningBox(this.$t('msgbox.invalidLngLat'), 'error')
          return
        }

        if (this.cmdTarget.groud.length === 0 && this.cmdTarget.device.length === 0) {
          this.selectAll(true)
        }
        bfprocess.cb03(this.cmdTarget, this.cb03Cmd)
        this.disabledSendCmdFunc('cb03')
      },
      send_cb04_cmd() {
        if (this.cmdTarget.groud.length === 0 && this.cmdTarget.device.length === 0) {
          bfNotify.warningBox(this.$t('msgbox.selectTarget'), 'error')
          return
        }

        if (this.cb04Cmd.setCmd !== 9 && this.cb04Cmd.setCmd !== 0) {
          if (this.cb04Cmd.minLon === '' || this.cb04Cmd.minLat === '' || this.cb04Cmd.maxLon === '' || this.cb04Cmd.maxLat === '') {
            bfNotify.warningBox(this.$t('msgbox.selectLngLat'), 'error')
            return
          }

          // 可能输入非数字字符串
          if (isNaN(this.cb04Cmd.minLon) || isNaN(this.cb04Cmd.minLat) || isNaN(this.cb04Cmd.maxLon) || isNaN(this.cb04Cmd.maxLat)) {
            bfNotify.warningBox(this.$t('msgbox.invalidLngLat'), 'error')
            return
          }

          // 经纬度可能是用户输入数字字符串，需要转换为数字
          ;['minLon', 'minLat', 'maxLon', 'maxLat'].forEach(key => {
            this.cb04Cmd[key] = +this.cb04Cmd[key]
          })

          if (!lonLatValidate([this.cb04Cmd.minLon, this.cb04Cmd.minLat]) || !lonLatValidate([this.cb04Cmd.maxLon, this.cb04Cmd.maxLat])) {
            bfNotify.warningBox(this.$t('msgbox.invalidLngLat'), 'error')
            return
          }
        }

        bfprocess.cb04(this.cmdTarget, this.cb04Cmd)
        this.disabledSendCmdFunc('cb04')
      },
      send_cb05_cmd() {
        if (this.cmdTarget.groud.length === 0 && this.cmdTarget.device.length === 0) {
          bfNotify.warningBox(this.$t('msgbox.selectTarget'), 'error')
          return
        }

        if (this.cb05Cmd.setCmd === 1) {
          if (this.cb05Cmd.lon === '' || this.cb05Cmd.lat === '') {
            bfNotify.warningBox(this.$t('msgbox.selectLngLat'), 'error')
            return
          }

          // 可能输入非数字字符串
          if (isNaN(this.cb05Cmd.lon) || isNaN(this.cb05Cmd.lat)) {
            bfNotify.warningBox(this.$t('msgbox.invalidLngLat'), 'error')
            return
          }

          // 经纬度可能是用户输入数字字符串，需要转换为数字
          ;['lon', 'lat'].forEach(key => {
            this.cb05Cmd[key] = +this.cb05Cmd[key]
          })

          if (!lonLatValidate([this.cb05Cmd.lon, this.cb05Cmd.lat])) {
            bfNotify.warningBox(this.$t('msgbox.invalidLngLat'), 'error')
            return
          }
        }

        // 计算岗哨半径的经纬度差
        const latDiff = bfutil.calculateLatitudeRadiusDiff(this.cb05Cmd.radius)
        const lonDiff = bfutil.calculateLongitudeRadiusDiff(this.cb05Cmd.lat, this.cb05Cmd.radius)
        this.cb05Cmd.latDif = '' + bfutil.lngLatDiffLimit(latDiff)
        this.cb05Cmd.lonDif = '' + bfutil.lngLatDiffLimit(lonDiff)

        bfprocess.cb05(this.cmdTarget, this.cb05Cmd)
        this.disabledSendCmdFunc('cb05')
      },
      send_cb06_cmd() {
        if (this.cmdTarget.groud.length === 0 && this.cmdTarget.device.length === 0) {
          bfNotify.warningBox(this.$t('msgbox.selectTarget'), 'error')
          return
        }

        this.cb06Cmd.latDif = parseInt(this.cb06Cmd.latDif)
        this.cb06Cmd.lonDif = parseInt(this.cb06Cmd.lonDif)
        bfprocess.cb06(this.cmdTarget, this.cb06Cmd)
        this.disabledSendCmdFunc('cb06')
      },
      send_cb07_cmd() {
        if (this.cmdTarget.groud.length === 0 && this.cmdTarget.device.length === 0) {
          bfNotify.warningBox(this.$t('msgbox.selectTarget'), 'error')
          return
        }

        bfprocess.cb07(this.cmdTarget, this.cb07Cmd)
        this.disabledSendCmdFunc('cb07')
      },
      // cb08 语音监听，只能选呼
      sendCb08Cmd() {
        bfprocess.cb08(this.cmdTarget, this.cb08Cmd)
        this.disabledSendCmdFunc('cb08')
      },
      checkVoipServerOpenForSendCb08(count = 0) {
        // 联网通话未打开，尝试打开后再发指令
        bfglob.emit('openMenuItem', 'command/bfSpeaking', vm => {
          if (this.voipServerConnected) {
            this.sendCb08Cmd()
          } else {
            setTimeout(() => {
              if (++count < 20) {
                this.checkVoipServerOpenForSendCb08(count)
              }
            }, 500)
          }
        })
      },
      send_cb08_cmd() {
        if (this.cmdTarget.groud.length === 0 && this.cmdTarget.device.length === 0) {
          bfNotify.warningBox(this.$t('msgbox.selectTarget'), 'error')
          return
        }

        // 如果系统中心控制台未运行，则禁止发命令
        if (!this.qWebServer) {
          bfNotify.warningBox(this.$t('msgbox.cannotSendCb08Cmd'), 'warning')
          return
        }

        if (!bfglob.userInfo.setting.voipSpeakInfo || !bfglob.userInfo.setting.voipSpeakInfo.speaker) {
          bfNotify.warningBox(this.$t('msgbox.notSetNetworkCallAgent'), 'warning')
          return
        }

        this.checkVoipServerOpenForSendCb08()
      },
      send_cb09_cmd() {
        if (this.cmdTarget.groud.length > 0) {
          bfNotify.warningBox(this.$t('msgbox.canNotDeliveredByGroup'), 'error')
          return
        }
        if (this.cmdTarget.device.length === 0) {
          bfNotify.warningBox(this.$t('msgbox.selectTarget'), 'error')
          return
        }

        bfprocess.cb09(this.cmdTarget, this.cb09Cmd)
        this.disabledSendCmdFunc('cb09')
      },
      send_cb10_cmd() {
        if (this.cmdTarget.groud.length === 0 && this.cmdTarget.device.length === 0) {
          bfNotify.warningBox(this.$t('msgbox.selectTarget'), 'error')
          return
        }
        bfprocess.cb10(this.cmdTarget)
        this.disabledSendCmdFunc('cb10')
      },
      send_cb21_cmd() {
        if (this.cmdTarget.groud.length === 0 && this.cmdTarget.device.length === 0) {
          bfNotify.warningBox(this.$t('msgbox.selectTarget'), 'error')
          return
        }
        bfprocess.cb21(this.cmdTarget, this.cb21Cmd)
        this.disabledSendCmdFunc('cb21')
      },
      send_bc31_cmd() {
        if (this.cmdTarget.groud.length === 0 && this.cmdTarget.device.length === 0) {
          bfNotify.warningBox(this.$t('msgbox.selectTarget'), 'error')
          return
        }
        if (!this.bc31Cmd.message) {
          bfNotify.warningBox(this.$t('msgbox.smsNotEmpty'), 'warning')
          return
        }

        bfprocess.bc31(this.cmdTarget, this.bc31Cmd)
        this.disabledSendCmdFunc('bc31')
      },
      send_cb25_cmd() {
        if (this.cmdTarget.groud.length === 0 && this.cmdTarget.device.length === 0) {
          bfNotify.warningBox(this.$t('msgbox.selectTarget'), 'error')
          return
        }

        if ((this.cb25Cmd.setCmd === 0 || this.cb25Cmd.setCmd === 1) && !this.cb25Cmd.gpsPoint) {
          bfNotify.warningBox(this.$t('msgbox.selectGpsPoint'), 'error')
          return
        }
        // 计算岗哨半径的经纬度差
        this.cb25Cmd.latDif = '' + bfutil.calculateLatitudeRadiusDiff(this.cb25Cmd.radius)
        this.cb25Cmd.lonDif = '' + bfutil.calculateLongitudeRadiusDiff(this.cb25Cmd.lat, this.cb25Cmd.radius)

        bfprocess.cb25(this.cmdTarget, this.cb25Cmd)
        this.disabledSendCmdFunc('cb25')
      },
      updateServerConnectedStatus(val) {
        this.voipServerConnected = val
      },

      addOneDeviceNode(device) {
        bftree.addOneDeviceNode(this.sendCmdTreeId, device, { selected: false })
        bftree.renderOrgCounter(this.sendCmdTreeId)
      },
      delOneDeviceNode(device) {
        bftree.delOneDeviceNode(this.sendCmdTreeId, device)
        bftree.renderOrgCounter(this.sendCmdTreeId)
      },
      updateOneDeviceNode(device) {
        bftree.updateOneDeviceNode(this.sendCmdTreeId, device)
        bftree.renderOrgCounter(this.sendCmdTreeId)
      },
      addOneOrgNode(orgData) {
        bftree.addOneOrgNode(this.sendCmdTreeId, orgData, { selected: false })
        bftree.renderOrgCounter(this.sendCmdTreeId)
      },
      delOneOrgNode(orgData) {
        bftree.delOneOrgNode(this.sendCmdTreeId, orgData)
        bftree.renderOrgCounter(this.sendCmdTreeId)
      },
      updateOneOrgNode(orgData) {
        bftree.updateOneOrgNode(this.sendCmdTreeId, orgData)
        bftree.renderOrgCounter(this.sendCmdTreeId)
      },
      treeSortChildren() {
        bftree.sortChildren(this.sendCmdTreeId)
      },
      // rangeDeviation() {
      //   this.mapVisible = true
      //   // 触发子组件baseMap的绘制当前设备的标记点和所选经纬度返回的矩形
      //   this.$refs.baseMap.drawPointAndRange()
      // }
    },
    computed: {
      cb42CmdOptions() {
        // code [uint8]: 10:关闭 11:开启 12:查询
        return [
          { label: this.$t('dialog.close'), value: 10 },
          { label: this.$t('dialog.turnOn'), value: 11 },
          { label: this.$t('dialog.query'), value: 12 },
        ]
      },
      lonLatControlIconEl() {
        return '<span class="mdi mdi-map-marker-radius text-xl"></span>'
      },
      mapControls() {
        return [
          {
            id: 'selectLngLatControl',
            position: 'top-right',
            control: this.selectLngLatControl,
          },
        ]
      },
      defaultWidth() {
        return '120px'
      },
      defaultLabelWidth() {
        return this.isFR || this.isEN ? '180px' : this.defaultWidth
      },
      spaceTimeLabelWidth() {
        return this.isFR ? '160px' : this.defaultWidth
      },
      cb25PointLabelWidth() {
        return this.isFR ? '230px' : this.defaultLabelWidth
      },
      // 是否有RFID巡查授权
      hasRfidAuth() {
        const license = getLicense()
        if (!checkLicenseAuthorized(license)) {
          return false
        }
        return checkLicenseWithModuleName(license.lic?.licenses ?? {}, LicenseModuleNames.ModRfid)
      },
      noRfidAuthToolTipText() {
        const i18nKey = getAuthModuleI18nKey(LicenseModuleNames.ModRfid)
        return this.$t('auth.noSpecifiedModuleAuth', { module: this.$t(i18nKey) })
      },
      tableTree() {
        return this.$refs[this.sendCmdTreeId]
      },
      treeOption() {
        return {
          selectMode: 2,
        }
      },
      filterOption() {
        return {
          leavesOnly: false,
        }
      },
      contextmenuOption() {
        return {
          delegate: 'span.fancytree-node',
          autoFocus: true,
          menu: [
            {
              title: this.$t('tree.online'),
              cmd: 'online',
            },
            {
              title: this.$t('tree.displayAllDev'),
              cmd: 'showAll',
            },
          ],
          select: (event, ui) => {
            switch (ui.cmd) {
              case 'online':
                this.$refs[this.sendCmdTreeId]?.showOnlineDevices()
                break
              case 'showAll':
                this.$refs[this.sendCmdTreeId]?.showAllDevices()
                break
            }
          },
        }
      },
      smsTypeList() {
        return [
          {
            label: this.$t('dialog.textInfo'),
            value: '02',
          },
          {
            label: this.$t('dialog.autoPlaySms'),
            value: '12',
          },
        ]
      },
    },
    watch: {
      'qWebChannel.server': {
        immediate: true,
        handler(val) {
          this.qWebServer = val
        },
      },
    },
    components: {
      BaseMap,
      TableTree,
    },
    mounted() {
      bfglob.vsendcmd = this
      this.openDlgFn()
      bfglob.on('updateDeviceNodeTitle', this.update_tree_node)
      // 监听重新过滤在线设备事件
      bfglob.on('voipServerConnected', this.updateServerConnectedStatus)

      // 同步树节点的增、删、改操作
      bfglob.on('addOneDeviceNode', this.addOneDeviceNode)
      bfglob.on('delOneDeviceNode', this.delOneDeviceNode)
      bfglob.on('updateOneDeviceNode', this.updateOneDeviceNode)
      bfglob.on('addOneOrgNode', this.addOneOrgNode)
      bfglob.on('delOneOrgNode', this.delOneOrgNode)
      bfglob.on('updateOneOrgNode', this.updateOneOrgNode)

      bfglob.on('treeSortChildren', this.treeSortChildren)

      // bfglob.on('rangeDeviation', this.rangeDeviation)
    },
    beforeUnmount() {
      lngLatMap = undefined
      bfglob.off('updateDeviceNodeTitle', this.update_tree_node)
      bfglob.off('voipServerConnected', this.updateServerConnectedStatus)

      bfglob.off('addOneDeviceNode', this.addOneDeviceNode)
      bfglob.off('delOneDeviceNode', this.delOneDeviceNode)
      bfglob.off('updateOneDeviceNode', this.updateOneDeviceNode)
      bfglob.off('addOneOrgNode', this.addOneOrgNode)
      bfglob.off('delOneOrgNode', this.delOneOrgNode)
      bfglob.off('updateOneOrgNode', this.updateOneOrgNode)
      bfglob.off('treeSortChildren', this.treeSortChildren)
      // bfglob.off('rangeDeviation', this.rangeDeviation)
    },
    activated() {
      this.$route.params = getRouteParams(this.$route.name)
      if (this.$route.params.isSelect) {
        this.cmdRadio = this.$route.params.radio
        setTimeout(() => {
          bftree.selectAll('sendCmdTree', false)
          const node = bftree.getTreeNodeByRid('sendCmdTree', this.$route.params.target)
          if (!node) {
            return
          }
          node.setSelected(true)
          node.makeVisible({ scrollIntoView: true })
        })
      }
    },
  }
</script>

<style lang="scss">
  .el-main.page-send-command .send-command-container {
    width: 1024px;
    margin: auto;

    .send-command-tree {
      border: 1px solid #ddd;
      background: #fff;
      height: 100%;
      width: 420px;
      overflow: hidden;
      position: relative;
      border-radius: 4px;
      flex: none !important;
    }

    .send-command-content {
      .el-radio-group.command-radio-group {
        width: 420px;
        margin: 0 auto;
        display: flex;
        gap: 10px;
        flex-wrap: wrap;
        justify-content: space-between;
        align-items: center;

        @media (max-height: 768px) {
          gap: 2px;
        }

        .el-radio {
          flex-basis: 180px;
          margin: 0;
          height: 24px;
          line-height: 1.5;
          display: inline-flex;
          align-items: center;
        }
      }

      .command-box {
        border-top: 1px solid #dcdfe6;
        overflow: auto;

        .el-form {
          width: 420px;
          margin: auto;

          .el-form-item .el-form-item__label {
            padding-bottom: 0 !important;
          }

          .sendCmdHeightMax768 {
            @media (max-height: 768px) {
              display: grid;
              grid-template-columns: repeat(2, minmax(0, 1fr));
              column-gap: 12px;
            }
          }

          .el-form-item--small.el-form-item {
            margin-bottom: 10px;
          }
        }

        .el-form-item.action-form-item .el-form-item__content {
          display: flex;
          justify-content: center;

          .el-button.fr {
            @apply w-36;
          }
        }
      }
    }
  }

  .el-main.page-send-command .send-command-map-container {
    position: fixed;
    inset: 0;
    z-index: 1000;
    background-color: #fff;
  }
</style>
