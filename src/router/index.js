import { createRouter, createWebHashHistory } from 'vue-router'
import { dataManageNavItemConfig } from './dataNav'
import { dispatchNavItemConfig } from './dispatchNav'
import i18n from '@/modules/i18n'
import { computed } from 'vue'
import { unref } from 'vue'

const { t } = i18n.global

export const LoginRouteName = 'login'
export const DataManageRouteName = 'dataManage'
export const DispatchRouteName = 'dispatch'

const dataManageComponents = import.meta.glob(['@/platform/dataManage/*.vue', '@/platform/dataManage/*/*.vue'])
const dispatchComponents = import.meta.glob(['@/platform/dispatch/*.vue', '@/platform/dispatch/*/*.vue'])

// 不需要跳转路由的路由名称, 点击该路由时判断不用跳转路由即可打开弹窗或其他操作
const unJumpRouteNames = ['authorization', 'systemVersion']

/**
 * 创建子路由配置
 * @param {string} parentDir - 父路由名称, 例如 'dataManage/phoneManage'
 * @returns {Array} 子路由配置数组
 */
function createChildRoutes(parentDir, allChildComponents) {
  return Object.keys(allChildComponents)
    .filter(path => path.includes(`/platform/${parentDir}/`))
    .map(path => {
      // 从路径中提取组件名
      const match = path.match(new RegExp(`^/src/platform/${parentDir}/(.*)\\.vue$`))
      const componentName = match?.[1]?.replace(/\.vue$/, '') ?? 'unknown'
      return {
        name: componentName,
        path: componentName,
        component: allChildComponents[path],
        meta: {
          unJumpRoute: unJumpRouteNames.includes(componentName),
        },
      }
    })
}

/**
 * 创建路由配置
 * @param {string} key - 路由键名
 * @param {Object} config - 导航配置
 * @param {string} parentRoute - `key`的父路由名称
 * @param {Object} allChildComponents - `parentRoute`的所有子组件
 * @returns {import('node_modules/vue-router/dist/vue-router').RouteRecordRaw} 路由配置对象
 */
function createRoute(key, config, parentRoute, allChildComponents) {
  const route = {
    path: key,
    name: key,
    meta: {
      navItemConfig: config,
    },
  }

  // 如果有默认子路径，创建子路由
  const configUnref = unref(config)
  const pathWithParentRoute = parentRoute + '/' + key
  if (configUnref.defaultChildPath) {
    route.redirect = `/${pathWithParentRoute}/${configUnref.defaultChildPath}`
    route.children = createChildRoutes(pathWithParentRoute, allChildComponents)
  } else {
    // 直接组件路由
    route.component = allChildComponents[`/src/platform/${pathWithParentRoute}.vue`]
  }

  return route
}

const dataManageRoutes = Object.entries(dataManageNavItemConfig).map(([key, config]) => createRoute(key, config, DataManageRouteName, dataManageComponents))
const dispatchRoutes = Object.entries(dispatchNavItemConfig).map(([key, config]) => createRoute(key, config, DispatchRouteName, dispatchComponents))
// 路由配置
const routes = [
  {
    path: '/upgrade',
    name: 'upgrade',
    component: () => import('@/layouts/upgrade.vue'),
  },
  {
    path: '/login',
    name: LoginRouteName,
    component: () => import('@/layouts/BfLogin.vue'),
  },
  {
    path: '/' + DataManageRouteName,
    name: DataManageRouteName,
    component: () => import('@/layouts/AdminLayout.vue'),
    redirect: '/' + DataManageRouteName + '/Orgs',
    children: [...dataManageRoutes],
    meta: {
      navItemConfig: computed(() => ({
        label: t('nav.AdminPlatform'),
        order: 1,
      })),
    },
  },
  {
    path: '/' + DispatchRouteName,
    name: DispatchRouteName,
    component: () => import('@/layouts/DispatchLayout.vue'),
    redirect: '/' + DispatchRouteName + '/CommunicationDispatch',
    children: [...dispatchRoutes],
  },
  {
    path: '/:pathMatch(.*)*', //  Vue Router 4 使用  /:pathMatch(.*)*  替代了原来的 '*'
    name: 'NotFound', // 建议添加一个 name
    component: () => import('@/layouts/Error404.vue'),
    meta: {
      hidden: true, //  保留 hidden 属性，方便后续使用
    },
  },
]
console.log('routes', routes)

// 创建路由实例
const router = createRouter({
  history: createWebHashHistory(), // 使用 hash 模式 (兼容性更好)
  routes, // 路由配置
})

// 路由守卫
router.beforeEach(async (to, from, next) => {
  bfglob.console.log('router.beforeEach', to, from)
  bfglob.console.log('login status', bfglob.isLogin)

  // 提示用户升级浏览器页面
  if (to.name === 'upgrade') {
    next()
    return
  }

  if (to.query?.sysId) {
    if (from.query.sysId && to.query.sysId !== from.query.sysId) {
      location.reload()
      return
    }

    if (!bfglob.isLogin) {
      if (to.name === LoginRouteName) {
        next()
      } else {
        next({ name: LoginRouteName, query: { sysId: to.query.sysId } })
      }
      return
    }

    next()
    return
  }

  // 防止死循环：如果你正跳转的路径已经是当前路径且 sysId 是默认值，就不跳了
  if (to.query?.sysId !== bfglob.sysId) {
    next({
      name: to.name || undefined,
      path: to.path,
      params: to.params,
      query: { ...to.query, sysId: bfglob.sysId || '00' },
      replace: true,
    })
    return
  }

  next() // fallback
})

/**
 * vue-router >= 4.1.4，不再使用params传递参数
 * 为用兼容当前vue2的项目语法，自定义一个路由参数的hook
 * @returns {{
 * setRouteParams: (function(routeKey: string, params: any): void),
 * getRouteParams: (function(routeKey: string, {clear: boolean}): any),
 * }}
 */
export function useRouteParams() {
  const prefix = 'vue_router_params_'

  function setRouteParams(routeKey, params) {
    sessionStorage.setItem(`${prefix}${routeKey}`, JSON.stringify(params))
  }

  function getRouteParams(routeKey, { clear = true } = {}) {
    let params = {}
    const paramsJson = sessionStorage.getItem(`${prefix}${routeKey}`)
    if (!paramsJson) return params

    // 清除参数
    if (clear) sessionStorage.removeItem(`${prefix}${routeKey}`)

    try {
      params = JSON.parse(paramsJson)
    } catch (e) {
      console.error('getRouteParams error:', e)
    }
    return params
  }

  return {
    setRouteParams,
    getRouteParams,
  }
}

export default router
