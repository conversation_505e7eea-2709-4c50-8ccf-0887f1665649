<template>
  <bf-dialog
    v-model="phoneGatewayVisible"
    :title="$t('dialog.setupPhoneInfo')"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    append-to-body
    center
    class="header-border phone-gateway-info-dialog"
    @close="onClose"
  >
    <el-table :data="tableData" :size="tableSize" border class="controller-gateway-table bf-table" style="width: 100%">
      <el-table-column :label="$t('dialog.interfacePos')" width="100" align="center">
        <template #default="scope">
          <span v-if="scope.row.edit" v-text="scope.row.phonePos" />
          <span v-else class="" v-text="scope.row.phonePos" />
        </template>
      </el-table-column>
      <el-table-column :label="$t('dialog.telephoneGateway')" align="center">
        <template #default="scope">
          <bf-select v-if="scope.row.edit" v-model="scope.row.refDevId" :size="formSize" filterable>
            <el-option v-for="item in refDevIdList" :key="item.rid" :label="item.label" :value="item.rid" />
          </bf-select>
          <span v-else class="" v-text="getRefDevName(scope.row.refDevId)" />
        </template>
      </el-table-column>
      <el-table-column :label="$t('dialog.telephoneNo')" align="center">
        <template #default="scope">
          <bf-input v-if="scope.row.edit" class="!h-[32px] !shadow-none" v-model="scope.row.phoneNo" :size="formSize" :maxlength="32" />
          <span v-else class="" v-text="scope.row.phoneNo" />
        </template>
      </el-table-column>
      <el-table-column width="180" :label="$t('dialog.action')" align="center">
        <template #default="scope">
          <div v-if="scope.row.edit" class="flex justify-center gap-4">
            <span
              :key="'save-btn' + scope.$index"
              class="iconfont bfdx-chenggong !text-[20px] text-[#1398E9] cursor-pointer"
              @click.stop.prevent="saveEditData(scope.$index, scope.row)"
            ></span>
            <span
              :key="'cancel-btn' + scope.$index"
              class="iconfont bfdx-biaogeguanbi !text-[20px] text-[#FF4E4E] cursor-pointer"
              @click.stop.prevent="cancelEditData(scope.$index, scope.row)"
            ></span>
          </div>
          <div v-else class="flex justify-center gap-4">
            <span
              :key="'edit-btn' + scope.$index"
              class="iconfont bfdx-biaogebianjianniu !text-[20px]"
              :class="[editing ? 'cursor-not-allowed text-[#1398E980]' : 'cursor-pointer text-[#1398E9]']"
              @click.stop.prevent="() => !editing && setRowEditStatus(scope.$index, scope.row, true)"
            ></span>
            <span
              :key="'edit-btn' + scope.$index"
              class="iconfont bfdx-biaogeshanchushannan !text-[20px]"
              :class="[editing || !scope.row.refDevId || !scope.row.phoneNo ? 'cursor-not-allowed text-[#FF4E4E80]' : 'cursor-pointer text-[#FF4E4E]']"
              @click.stop.prevent="() => !(editing || !scope.row.refDevId || !scope.row.phoneNo) && deleteRowData(scope.$index, scope.row)"
            ></span>
          </div>
        </template>
      </el-table-column>
    </el-table>
  </bf-dialog>
</template>

<script>
  import bfutil, { DeviceTypes } from '@/utils/bfutil'
  import bfNotify from '@/utils/notify'
  import validateRules from '@/utils/validateRules'
  import AsyncValidator from 'async-validator'
  import { cloneDeep } from 'lodash'
  import bfDialog from '@/components/bfDialog/main'
  import bfInput from '@/components/bfInput/main'
  import bfSelect from '@/components/bfSelect/main'

  const editDataModel = {
    // 控制器所属rid
    orgId: '',
    // 终端的rid
    refDevId: '',
    // 控制器的rid
    refControllerId: '',
    // 电话号码
    phoneNo: '',
    // 插槽位置，从1开始
    phonePos: '',

    // 辅助参数
    // 当前行是否开启编辑模式
    edit: false,
  }
  const compareKeys = ['phonePos', 'refDevId', 'phoneNo']

  export default {
    name: 'ControllerGateway',
    emits: ['update:modelValue', 'update:visible'],
    props: {
      visible: {
        type: Boolean,
        required: true,
      },
      modelValue: {
        type: Array,
        required: true,
      },
      // 是否为添加模式
      isNewMode: {
        type: Boolean,
        default: true,
      },
      // medium / small / default
      tableSize: {
        type: String,
        default: 'default',
      },
      // medium / small / default
      formSize: {
        type: String,
        default: 'default',
      },
      // 控制器rid, 如果是添加控制器时为空
      refControllerId: {
        type: String,
        default: '',
      },
      // 控制器所属单位rid
      orgId: {
        type: String,
        default: '',
      },

      // 数据库操作方法
      addDataMethod: {
        type: Function,
        default: (_data, _cb) => {},
      },
      upDataMethod: {
        type: Function,
        default: (_data, _cb) => {},
      },
      delDataMethod: {
        type: Function,
        default: (_data, _cb) => {},
      },
      requestDataMethod: {
        type: Function,
        default: (_data, _cb) => {},
      },
    },
    components: {
      bfDialog,
      bfInput,
      bfSelect,
    },
    data() {
      return {
        tableData: [],
        deviceDataList: bfglob.gdevices.getList(),
        controllerGateway: bfglob.gcontrollerGateway.getAll(),
        posLimit: 10,
      }
    },
    methods: {
      getRefDevName(rid) {
        const key = 'x' + rid
        return this.deviceDataList[key]?.label ?? rid
      },
      onClose() {
        // 新添加控制器，则返回所有配置好的数据
        if (this.isNewMode) {
          const data = this.tableData.filter(item => !this.isEmptyRow(item))
          this.$emit('update:modelValue', data)
        }
        this.phoneGatewayVisible = false
      },
      getNewRowData() {
        return Object.assign({}, editDataModel, {
          refControllerId: this.refControllerId,
          orgId: this.orgId,
          edit: false,
        })
      },
      initTableData() {
        const data = []
        for (let i = 0; i < this.posLimit; i++) {
          const config = Object.assign(this.getNewRowData(), {
            phonePos: i + 1,
          })
          data.push(config)
        }
        this.tableData = data
      },
      requestDataByControllerId() {
        this.requestDataMethod(this.refControllerId).then(dataList => {
          this.asyncTableData(cloneDeep(dataList))
        })
      },
      getLocalData(rid) {
        const data = Object.keys(this.controllerGateway)
          .filter(key => {
            return this.controllerGateway[key].refControllerId === rid
          })
          .map(key => {
            return this.controllerGateway[key]
          })

        return cloneDeep(data)
      },
      initGatewayData() {
        // refControllerId参数存在，则表示为更新操作
        if (this.refControllerId) {
          const data = this.getLocalData(this.refControllerId)
          if (data.length > 0) {
            this.asyncTableData(data)
          } else {
            this.requestDataByControllerId()
          }
        } else {
          // 因为添加控制器时，还没有rid,默认为空字符串
          // 判断是否有添加相关设置的信息，有则显示，而非显示空白数据
          this.asyncTableData(this.modelValue)
        }
      },
      // 按接口位置排序
      sortTableData(data = this.tableData) {
        data.sort((a, b) => {
          return bfutil.sortByProps(a, b, { phonePos: 'asc' })
        })
        // const tableData = data.sort((a, b) => {
        //   return bfutil.sortByProps(a, b, { phonePos: 'asc' })
        // })
        // this.tableData = Object.assign([], tableData)
      },
      // 同步本组件数据源
      asyncTableData(data) {
        for (let i = 0; i < data.length; i++) {
          const item = data[i]
          if (item.phonePos > this.posLimit) {
            continue
          }
          item.edit = false
          this.tableData[item.phonePos - 1] = item
        }

        this.sortTableData(this.tableData)
      },
      setRowEditStatus(index, row, status) {
        this.tableData[index] = {
          ...row,
          edit: status,
        }
      },
      /**
       * 重置一行数据
       * @param {number} index 行索引
       * @param {Record<string, any>?} row 表格的一行数据
       */
      resetTableRow(index, row) {
        this.tableData[index] = Object.assign(this.getNewRowData(), {
          ...row,
          phonePos: index + 1,
          edit: false,
        })
      },
      // 删除一行
      async deleteRowData(index, row) {
        // 更新控制器，直接删除数据库数据
        if (!this.isNewMode && row.rid) {
          const isOk = await this.deleteDataFunc(index, row)
          if (!isOk) return
        }

        // 重置本地数据
        this.resetTableRow(index)
      },
      // 检查是否空行数据
      isEmptyRow(data) {
        return !(data.refDevId || data.phoneNo)
      },
      // 表单规则检验
      validatorRow(index, data) {
        return new Promise(resolve => {
          const rules = () => {
            const requiredMsg = this.$t('dialog.requiredRule')
            const refDevIdRequiredMsg = this.$t('dialog.telephoneGateway') + ' ' + requiredMsg
            const phoneNoRequiredMsg = this.$t('dialog.telephoneNo') + ' ' + requiredMsg
            return {
              refDevId: [
                validateRules.required('blur', refDevIdRequiredMsg),
                {
                  validator: (rule, value, callback) => {
                    if (this.checkRefDevIdIsUsed(index, data, value)) {
                      callback(new Error(this.$t('msgbox.controllerGatewayUniqueDevice')))
                    } else {
                      callback()
                    }
                  },
                  trigger: 'blur',
                },
              ],
              phoneNo: [validateRules.required('blur', phoneNoRequiredMsg), validateRules.telephoneNumber()],
            }
          }
          const validator = new AsyncValidator(rules())

          validator.validate(data, { first: true }, (errors /*, fields*/) => {
            if (errors) {
              errors.forEach(item => {
                bfNotify.messageBox(item.message, 'error')
              })
              resolve(false)
            } else {
              resolve(true)
            }
          })
        })
      },
      async saveEditData(index, row) {
        // 如果是空行，则取消编辑状态，
        if (this.isEmptyRow(row)) {
          this.setRowEditStatus(index, row, false)
          return
        }

        // 规则校验
        const valid = await this.validatorRow(index, row)
        if (!valid) return

        // 更新控制器模式，直接操作数据库
        if (!this.isNewMode) {
          // 没有rid参数，或者本地查找不到数据，视为新建
          const data = bfglob.gcontrollerGateway.get(row.rid)
          if (!row.rid || !data) {
            await this.addDataFunc(index, row)
            return
          }

          // 判断数据是否有变化，没有变化则不更新
          const isChange = compareKeys.some(key => row[key] !== data[key])
          if (isChange) {
            await this.updateDataFunc(index, row)
            return
          }
        }

        // 添加控制器模式，直接关闭编辑状态
        this.setRowEditStatus(index, row, false)
      },
      cancelEditData(index, row) {
        // 空数据，直接修改编辑状态
        if (this.isEmptyRow(row)) {
          this.setRowEditStatus(index, row, false)
          return
        }

        let originData
        // 找到编辑前的数据，如果有则恢复编辑前的数据，没有则重置
        if (!this.isNewMode && row.rid) {
          const data = bfglob.gcontrollerGateway.get(row.rid)
          if (data) {
            originData = cloneDeep(data)
          }
        }
        this.resetTableRow(index, originData)
      },

      // 更新数据库后操作
      insertUpdateDataAfter(index, row) {
        row.refDevName = bfglob.gdevices.getSelfIdByKey(row.refDevId)
        this.setRowEditStatus(index, row, false)
      },
      // 添加到数据库
      async addDataFunc(index, row) {
        try {
          row = await this.addDataMethod(row)
          this.insertUpdateDataAfter(index, row)
          bfNotify.messageBox(this.$t('msgbox.addSuccess'), 'success')
          return row
        } catch (_e) {
          // no-empty
        }
      },
      // 更新到数据库
      async updateDataFunc(index, row) {
        try {
          row = await this.upDataMethod(row)
          this.insertUpdateDataAfter(index, row)
          return row
        } catch (_e) {
          // no-empty
        }
      },
      // 从数据库删除
      async deleteDataFunc(index, row) {
        return this.delDataMethod(row)
          .then(() => true)
          .catch(() => false)
      },
      // 检测refDevId是否已被使用
      checkRefDevIdIsUsed(index, data, refDevId) {
        // 先判断本地是否已经有数据
        const row = this.tableData.find((item, i) => item.refDevId === refDevId && i !== index)
        if (row) {
          return true
        }

        // 再判断本地全局数据是否已经存在
        // 只要终端rid相同，但rid不同，则为两个数据
        const gateway = bfglob.gcontrollerGateway.getDataByDevId(refDevId)
        return gateway?.rid !== data.rid
      },
    },
    computed: {
      // 是否有数据在编辑中
      editing() {
        return this.tableData.some(item => item.edit === true)
      },
      fullscreen() {
        return this.$root.layoutLevel === 0
      },
      phoneGatewayVisible: {
        get() {
          return this.visible
        },
        set(val) {
          this.$emit('update:visible', val)
        },
      },
      refDevIdList() {
        const types = [DeviceTypes.PhoneRepeater, DeviceTypes.AnalogGateway, DeviceTypes.DigitalGateway]
        return Object.keys(this.deviceDataList)
          .map(key => {
            return this.deviceDataList[key]
          })
          .filter(item => {
            return types.includes(item.deviceType)
          })
      },
    },
    beforeMount() {
      this.initTableData()
      this.initGatewayData()
    },
  }
</script>

<style lang="scss">
  .el-dialog.phone-gateway-info-dialog {
    width: 680px;
  }

  .controller-gateway {
    height: 52vh;

    .el-card__header {
      flex: auto;
      flex-grow: 0;
      padding: 10px 15px;
      display: flex;
      align-items: center;

      .head-title {
        flex: auto;
      }

      .close-btn {
        flex: none;
      }
    }

    .el-card__body {
      flex: auto;
      height: calc(100% - 41px);
    }
  }

  .controller-gateway.is-mobile,
  .el-dialog.is-fullscreen .controller-gateway,
  .controller-gateway-table {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  .controller-gateway-table {
    .el-table__header-wrapper {
      flex: none;
    }

    .el-table__body-wrapper {
      flex: auto;
      overflow: auto;
    }

    &.el-table td,
    &.el-table th {
      padding: 0;
      height: 40px;
      line-height: 40px;
    }

    &.el-table td .cell {
      padding: 3px 6px;
      line-height: 30px;
    }
  }
</style>
