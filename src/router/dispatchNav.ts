import { computed, ComputedRef } from 'vue'
import { NavItemConfig } from './dataNav'
import i18n from '@/modules/i18n'
const { t } = i18n.global

export const DispatchImgFolderName = 'dispatch'

export const dispatchNavItemConfig: Record<string, ComputedRef<NavItemConfig>> = {
  GisApplication: computed(() => ({
    label: t('nav.GisApplication'),
    order: 1,
  })),
  CommunicationDispatch: computed(() => ({
    label: t('nav.CommunicationDispatch'),
    order: 2,
  })),
  DataApplication: computed(() => ({
    label: t('nav.DataApplication'),
    order: 3,
  })),
}
