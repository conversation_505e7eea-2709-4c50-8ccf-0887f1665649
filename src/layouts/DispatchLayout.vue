<template>
  <el-container class="common-bg w-full h-full !flex-col app-layout">
    <!-- 顶部导航 -->
    <BGHead class="flex-none" />

    <!-- 路由页面 -->
    <router-view v-slot="{ Component }">
      <transition name="fade-transform" mode="out-in">
        <!-- 使用 keep-alive 缓存路由组件，确保组件状态保持 -->
        <keep-alive>
          <component :is="Component" />
        </keep-alive>
      </transition>
    </router-view>
  </el-container>
</template>

<script setup>
  import '@/modules/dataManager'
  import BGHead from '@/layouts/BFHead.vue'
  import bfprocess from '@/utils/bfprocess.js'

  bfprocess.loginedAfterFunc()
</script>

<style scoped lang="scss"></style>
