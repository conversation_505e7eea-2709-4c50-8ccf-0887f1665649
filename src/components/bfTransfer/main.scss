@use '@/css/common.scss';
@use '@/components/bfInput/main.scss' as bfInput;
@use '@/assets/bfdxFont/iconfont.css';

.el-transfer.bf-transfer {
  @extend .bf-component-size;

  // 基础样式变量
  --el-transfer-border-color: rgba(148, 204, 232, 1);
  --el-transfer-border-radius: 0;
  --el-transfer-panel-header-bg-color: rgba(6, 121, 204, 0.46);
  --el-transfer-pane-border-size: 1px;

  --el-checkbox-checked-text-color: #fff;
  --el-checkbox-text-color: #1ddbff;
  --el-checkbox-font-size: unset;
  --el-disabled-text-color: rgba(29, 219, 255, 0.5);

  .el-transfer-panel {
    border: var(--el-transfer-pane-border-size) solid var(--el-transfer-border-color);
    border-radius: var(--el-transfer-border-radius);
    background-color: transparent;

    .el-checkbox {

      // 在transfer中使用@extend .bf-checkbox;来继承checkbox的样式就是失败，但是直接复制一份过来就可以。
      // @extend .bf-checkbox;
      .el-checkbox__input {
        background-color: unset;
        border-color: unset;
        top: 0 !important;

        .el-checkbox__inner {
          background-size: 100% 100%;
          background-repeat: no-repeat;
          background-color: unset;
          border: unset;
          width: 100%;
          height: 100%;

          @extend .iconfont;
          @extend .bfdx-waikuang1;

          font-size: inherit;
          color: var(--el-checkbox-text-color);

          &::after {
            content: '';
            width: 0;
            height: 0;
            border: unset;
          }
        }

        &.is-indeterminate {
          .el-checkbox__inner {
            @extend .iconfont;
            @extend .bfdx-xuanzhong;

            &::before {
              background-color: unset;
              position: static;
              height: unset;
              transform: none;
            }
          }
        }

        &.is-checked {
          background-color: unset;
          border-color: unset;

          .el-checkbox__inner {
            --el-checkbox-checked-bg-color: transparent;
            --el-checkbox-checked-input-border-color: transparent;

            @extend .bfdx-xuanzhong;
            color: var(--el-checkbox-checked-text-color);
          }

          &.is-disabled {
            .el-checkbox__inner {
              --el-checkbox-disabled-checked-input-fill: transparent;
            }
          }
        }

        &.is-disabled {
          .el-checkbox__inner {
            color: var(--el-disabled-text-color);
          }
        }
      }
    }

    .el-transfer-panel__header {
      background-color: var(--el-transfer-panel-header-bg-color);
      color: #fff;
      padding: 10px 15px;
      border: none;

      .el-checkbox .el-checkbox__label {
        color: #fff;
        span {
          color: #fff;
        }
      }
    }

    .el-transfer-panel__body {
      border: none;

      .el-input {
        @extend .bf-input;
      }
    }

    .el-transfer-panel__footer {
      background-color: transparent;
      border: none;
      border-top: 1px solid var(--el-transfer-border-color);
      display: flex;
      justify-content: center;
      gap: 16px;

      &::after {
        display: none;
      }
    }
  }

  .el-transfer__buttons {
    button {
      background-color: transparent;
      border: none;
      span::before {
        font-size: 26px;
      }
    }
    & > button:first-child {
      &>span:first-child {
        @extend .iconfont;
        @extend .bfdx-zuocaozuo;

        i {
          display: none;
        }
      }
    }
    & > button:last-child {
      &>span:first-child {
        @extend .iconfont;
        @extend .bfdx-youcaozuo;

        i {
          display: none;
        }
      }
    }
  }
}